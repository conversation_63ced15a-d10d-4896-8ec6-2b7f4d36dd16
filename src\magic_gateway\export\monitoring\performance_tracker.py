"""
Performance tracking service for export operations.

This module provides comprehensive performance monitoring for export operations,
including processing time, memory usage, data volume tracking, and progress indicators.
"""

import asyncio
import psutil
import time
from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List, AsyncContextManager
from contextlib import asynccontextmanager

from magic_gateway.core.logging_config import log
from magic_gateway.export.models import ExportFormat, ExportContext


@dataclass
class ExportPerformanceMetrics:
    """Performance metrics for export operations."""
    
    # Basic operation info
    job_id: int
    request_id: str
    format: ExportFormat
    operation_stage: str
    
    # Timing metrics
    start_time: datetime
    end_time: Optional[datetime] = None
    duration_seconds: Optional[float] = None
    
    # Data volume metrics
    total_rows_processed: int = 0
    data_volume_mb: float = 0.0
    rows_per_second: Optional[float] = None
    mb_per_second: Optional[float] = None
    
    # Memory metrics
    memory_usage_start_mb: float = 0.0
    memory_usage_peak_mb: float = 0.0
    memory_usage_end_mb: float = 0.0
    memory_delta_mb: float = 0.0
    
    # Progress tracking
    progress_indicators: List[Dict[str, Any]] = field(default_factory=list)
    
    # Additional context
    table_name: Optional[str] = None
    database_name: Optional[str] = None
    optimization_strategy: Optional[str] = None
    connection_pool_type: Optional[str] = None
    error_occurred: bool = False
    error_message: Optional[str] = None
    
    def calculate_derived_metrics(self) -> None:
        """Calculate derived performance metrics."""
        if self.end_time and self.start_time:
            self.duration_seconds = (self.end_time - self.start_time).total_seconds()
            
            if self.duration_seconds > 0:
                self.rows_per_second = self.total_rows_processed / self.duration_seconds
                self.mb_per_second = self.data_volume_mb / self.duration_seconds
        
        self.memory_delta_mb = self.memory_usage_end_mb - self.memory_usage_start_mb


class ExportPerformanceTracker:
    """
    Service for tracking export performance metrics.
    
    This class provides comprehensive performance monitoring including:
    - Processing time tracking
    - Memory usage monitoring
    - Data volume metrics
    - Progress indicators for large datasets
    - Integration with existing monitoring infrastructure
    """
    
    def __init__(self):
        """Initialize the performance tracker."""
        self._active_operations: Dict[str, ExportPerformanceMetrics] = {}
        self._completed_operations: List[ExportPerformanceMetrics] = []
        self._max_completed_history = 1000  # Keep last 1000 completed operations
        
        # Progress tracking configuration
        self._progress_interval_rows = 1_000_000  # Log progress every 1M rows
        self._memory_check_interval = 100_000  # Check memory every 100K rows
        
    @asynccontextmanager
    async def track_operation(
        self,
        export_context: ExportContext,
        operation_stage: str
    ) -> AsyncContextManager[ExportPerformanceMetrics]:
        """
        Context manager for tracking export operation performance.
        
        Args:
            export_context: Export context containing operation details
            operation_stage: Stage of the export operation being tracked
            
        Yields:
            ExportPerformanceMetrics object for the operation
        """
        # Create metrics object
        metrics = ExportPerformanceMetrics(
            job_id=export_context.job_id,
            request_id=str(export_context.request_id),
            format=export_context.format,
            operation_stage=operation_stage,
            start_time=datetime.now(timezone.utc),
            table_name=export_context.table_name,
            database_name=export_context.database_name,
            optimization_strategy=getattr(export_context.optimization_strategy, 'temp_file_strategy', None) if export_context.optimization_strategy else None,
            connection_pool_type=type(export_context.connection_manager).__name__ if export_context.connection_manager else None
        )
        
        # Record initial memory usage
        metrics.memory_usage_start_mb = self._get_memory_usage_mb()
        
        # Track as active operation
        operation_key = f"{export_context.job_id}_{export_context.request_id}_{operation_stage}"
        self._active_operations[operation_key] = metrics
        
        log.info(
            f"Started performance tracking for job {export_context.job_id} "
            f"stage '{operation_stage}' (request: {export_context.request_id})"
        )
        
        try:
            yield metrics
            
        except Exception as e:
            # Record error information
            metrics.error_occurred = True
            metrics.error_message = str(e)
            log.error(
                f"Error during tracked operation for job {export_context.job_id} "
                f"stage '{operation_stage}': {e}"
            )
            raise
            
        finally:
            # Finalize metrics
            metrics.end_time = datetime.now(timezone.utc)
            metrics.memory_usage_end_mb = self._get_memory_usage_mb()
            metrics.calculate_derived_metrics()
            
            # Move from active to completed
            if operation_key in self._active_operations:
                del self._active_operations[operation_key]
            
            self._completed_operations.append(metrics)
            
            # Trim completed operations history
            if len(self._completed_operations) > self._max_completed_history:
                self._completed_operations = self._completed_operations[-self._max_completed_history:]
            
            # Log completion metrics
            await self._log_completion_metrics(metrics)
    
    def record_progress(
        self,
        request_id: str,
        rows_processed: int,
        data_volume_mb: float,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Record progress for an ongoing export operation.
        
        Args:
            request_id: Request ID of the operation
            rows_processed: Number of rows processed so far
            data_volume_mb: Data volume processed in MB
            additional_context: Additional context information
        """
        # Find active operation
        active_metrics = None
        for metrics in self._active_operations.values():
            if metrics.request_id == request_id:
                active_metrics = metrics
                break
        
        if not active_metrics:
            log.warning(f"No active operation found for request_id: {request_id}")
            return
        
        # Update metrics
        active_metrics.total_rows_processed = rows_processed
        active_metrics.data_volume_mb = data_volume_mb
        
        # Check memory usage periodically
        if rows_processed % self._memory_check_interval == 0:
            current_memory = self._get_memory_usage_mb()
            if current_memory > active_metrics.memory_usage_peak_mb:
                active_metrics.memory_usage_peak_mb = current_memory
        
        # Log progress indicators for large datasets
        if rows_processed % self._progress_interval_rows == 0:
            progress_info = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "rows_processed": rows_processed,
                "data_volume_mb": data_volume_mb,
                "memory_usage_mb": self._get_memory_usage_mb(),
                "elapsed_seconds": (datetime.now(timezone.utc) - active_metrics.start_time).total_seconds()
            }
            
            if additional_context:
                progress_info.update(additional_context)
            
            active_metrics.progress_indicators.append(progress_info)
            
            # Calculate current processing rate
            elapsed_seconds = progress_info["elapsed_seconds"]
            if elapsed_seconds > 0:
                current_rows_per_second = rows_processed / elapsed_seconds
                current_mb_per_second = data_volume_mb / elapsed_seconds
                
                log.info(
                    f"Export progress for job {active_metrics.job_id}: "
                    f"{rows_processed:,} rows ({data_volume_mb:.1f} MB) processed "
                    f"at {current_rows_per_second:.0f} rows/sec, {current_mb_per_second:.1f} MB/sec "
                    f"(memory: {progress_info['memory_usage_mb']:.1f} MB) "
                    f"(request: {request_id})"
                )
    
    def get_active_operations(self) -> List[ExportPerformanceMetrics]:
        """Get list of currently active export operations."""
        return list(self._active_operations.values())
    
    def get_completed_operations(
        self,
        limit: Optional[int] = None,
        job_id: Optional[int] = None
    ) -> List[ExportPerformanceMetrics]:
        """
        Get list of completed export operations.
        
        Args:
            limit: Maximum number of operations to return
            job_id: Filter by specific job ID
            
        Returns:
            List of completed export performance metrics
        """
        operations = self._completed_operations
        
        if job_id is not None:
            operations = [op for op in operations if op.job_id == job_id]
        
        # Sort by end time (most recent first)
        operations = sorted(operations, key=lambda x: x.end_time or x.start_time, reverse=True)
        
        if limit:
            operations = operations[:limit]
        
        return operations
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """
        Get summary of export performance metrics.
        
        Returns:
            Dictionary containing performance summary statistics
        """
        active_count = len(self._active_operations)
        completed_count = len(self._completed_operations)
        
        if not self._completed_operations:
            return {
                "active_operations": active_count,
                "completed_operations": completed_count,
                "summary": "No completed operations available"
            }
        
        # Calculate summary statistics from completed operations
        successful_ops = [op for op in self._completed_operations if not op.error_occurred]
        failed_ops = [op for op in self._completed_operations if op.error_occurred]
        
        summary = {
            "active_operations": active_count,
            "completed_operations": completed_count,
            "successful_operations": len(successful_ops),
            "failed_operations": len(failed_ops),
            "success_rate": len(successful_ops) / completed_count if completed_count > 0 else 0,
        }
        
        if successful_ops:
            durations = [op.duration_seconds for op in successful_ops if op.duration_seconds]
            rows_processed = [op.total_rows_processed for op in successful_ops]
            data_volumes = [op.data_volume_mb for op in successful_ops]
            
            if durations:
                summary.update({
                    "avg_duration_seconds": sum(durations) / len(durations),
                    "min_duration_seconds": min(durations),
                    "max_duration_seconds": max(durations),
                })
            
            if rows_processed:
                summary.update({
                    "avg_rows_processed": sum(rows_processed) / len(rows_processed),
                    "total_rows_processed": sum(rows_processed),
                })
            
            if data_volumes:
                summary.update({
                    "avg_data_volume_mb": sum(data_volumes) / len(data_volumes),
                    "total_data_volume_mb": sum(data_volumes),
                })
        
        return summary
    
    def _get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB."""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            return memory_info.rss / (1024 * 1024)  # Convert bytes to MB
        except Exception as e:
            log.warning(f"Failed to get memory usage: {e}")
            return 0.0
    
    async def _log_completion_metrics(self, metrics: ExportPerformanceMetrics) -> None:
        """
        Log completion metrics for an export operation.
        
        Args:
            metrics: Completed export performance metrics
        """
        status = "FAILED" if metrics.error_occurred else "SUCCESS"
        
        # Handle None values safely
        duration = metrics.duration_seconds or 0.0
        rows_per_sec = metrics.rows_per_second or 0.0
        memory_delta = metrics.memory_delta_mb or 0.0
        
        log.info(
            f"Export {status} for job {metrics.job_id} stage '{metrics.operation_stage}': "
            f"duration={duration:.2f}s, "
            f"rows={metrics.total_rows_processed:,}, "
            f"data={metrics.data_volume_mb:.1f}MB, "
            f"rate={rows_per_sec:.0f} rows/sec, "
            f"memory_delta={memory_delta:.1f}MB "
            f"(request: {metrics.request_id})"
        )
        
        # Log detailed metrics at debug level
        log.debug(
            f"Detailed metrics for job {metrics.job_id}: "
            f"format={metrics.format.value}, "
            f"table={metrics.table_name}, "
            f"memory_start={metrics.memory_usage_start_mb:.1f}MB, "
            f"memory_peak={metrics.memory_usage_peak_mb:.1f}MB, "
            f"memory_end={metrics.memory_usage_end_mb:.1f}MB, "
            f"progress_points={len(metrics.progress_indicators)}, "
            f"connection_pool={metrics.connection_pool_type}, "
            f"optimization={metrics.optimization_strategy}"
        )


# Global performance tracker instance
export_performance_tracker = ExportPerformanceTracker()