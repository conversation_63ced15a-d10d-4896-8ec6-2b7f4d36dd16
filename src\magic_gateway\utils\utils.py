"""General utility functions for MagicGateway."""

from typing import Dict, Optional


from magic_gateway.core.logging_config import log
from magic_gateway.core.exceptions import PostgresException
from magic_gateway.db.postgres_handler import PostgresHandler


async def get_formatted_column_mapping() -> Dict[str, Dict[str, str]]:
    """
    Retrieve column mappings and format them for use with pg_parser library.

    Returns:
        Dictionary with 'global' key containing mapping from PostgreSQL to ClickHouse column names.
    """
    try:
        # Get raw mappings
        raw_mappings = await PostgresHandler.get_column_mapping()

        # Format into the structure expected by pg_parser
        formatted_mapping = {"global": {}}

        for mapping in raw_mappings:
            pg_col = mapping.get("colname_postgres")
            ch_col = mapping.get("colname_clickhouse")
            if pg_col and ch_col:
                formatted_mapping["global"][pg_col] = ch_col

        log.info(f"Formatted {len(formatted_mapping['global'])} column mappings")
        return formatted_mapping
    except Exception as e:
        log.error(f"Failed to format column mappings: {e}", exc_info=True)
        raise PostgresException(f"Failed to format column mappings: {e}") from e


def get_axis_type_from_view_name(full_view_name: str) -> Optional[str]:
    """
    Extract the axis type from a view's full name.

    The axis type is defined as the first 4 characters of the view name (without schema).

    Args:
        full_view_name: The full name of the view including schema (e.g., 'schema.view_name')

    Returns:
        The axis type (first 4 characters of view name) or None if the view name is too short
        or the input format is invalid.

    Examples:
        >>> get_axis_type_from_view_name('schema.axsm_view_name')
        'axsm'
        >>> get_axis_type_from_view_name('invalid')
        None
    """
    try:
        # Check if the full view name contains a schema separator
        if "." not in full_view_name:
            return None

        # Split the full view name by the schema separator
        parts = full_view_name.split(".")

        # Get the view name (last part after the schema)
        view_name = parts[-1]

        # Check if the view name is at least 4 characters long
        if len(view_name) < 4:
            return None

        # Return the first 4 characters as the axis type
        return view_name[:4]

    except Exception:
        # Return None for any unexpected errors
        return None
