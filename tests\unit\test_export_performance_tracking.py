"""
Unit tests for export performance tracking.

This module tests the export performance monitoring functionality,
including metrics collection, progress tracking, and integration
with existing monitoring infrastructure.
"""

import pytest
import asyncio
from datetime import datetime, timezone
from unittest.mock import Mock, AsyncMock, patch

from magic_gateway.export.models import ExportContext, ExportFormat, ExportOptions
from magic_gateway.export.monitoring import (
    ExportPerformanceMetrics,
    ExportPerformanceTracker,
    export_performance_tracker,
    ExportMonitoringIntegration,
    export_monitoring_integration
)


class TestExportPerformanceMetrics:
    """Test the ExportPerformanceMetrics data class."""
    
    def test_metrics_creation(self):
        """Test creating performance metrics."""
        metrics = ExportPerformanceMetrics(
            job_id=123,
            request_id="test-request-123",
            format=ExportFormat.CSV,
            operation_stage="test_stage",
            start_time=datetime.now(timezone.utc)
        )
        
        assert metrics.job_id == 123
        assert metrics.request_id == "test-request-123"
        assert metrics.format == ExportFormat.CSV
        assert metrics.operation_stage == "test_stage"
        assert metrics.total_rows_processed == 0
        assert metrics.data_volume_mb == 0.0
        assert not metrics.error_occurred
    
    def test_calculate_derived_metrics(self):
        """Test calculation of derived metrics."""
        start_time = datetime.now(timezone.utc)
        end_time = start_time.replace(second=start_time.second + 10)  # 10 seconds later
        
        metrics = ExportPerformanceMetrics(
            job_id=123,
            request_id="test-request-123",
            format=ExportFormat.CSV,
            operation_stage="test_stage",
            start_time=start_time,
            end_time=end_time,
            total_rows_processed=1000,
            data_volume_mb=10.0,
            memory_usage_start_mb=100.0,
            memory_usage_end_mb=150.0
        )
        
        metrics.calculate_derived_metrics()
        
        assert metrics.duration_seconds == 10.0
        assert metrics.rows_per_second == 100.0  # 1000 rows / 10 seconds
        assert metrics.mb_per_second == 1.0  # 10 MB / 10 seconds
        assert metrics.memory_delta_mb == 50.0  # 150 - 100


class TestExportPerformanceTracker:
    """Test the ExportPerformanceTracker class."""
    
    @pytest.fixture
    def tracker(self):
        """Create a fresh tracker instance for testing."""
        return ExportPerformanceTracker()
    
    @pytest.fixture
    def mock_export_context(self):
        """Create a mock export context for testing."""
        context = Mock(spec=ExportContext)
        context.job_id = 123
        context.request_id = "test-request-123"
        context.format = ExportFormat.CSV
        context.table_name = "test.table"
        context.database_name = "test"
        context.connection_manager = Mock()
        context.optimization_strategy = None
        return context
    
    @pytest.mark.asyncio
    async def test_track_operation_success(self, tracker, mock_export_context):
        """Test successful operation tracking."""
        async with tracker.track_operation(mock_export_context, "test_stage") as metrics:
            assert metrics.job_id == 123
            assert metrics.request_id == "test-request-123"
            assert metrics.format == ExportFormat.CSV
            assert metrics.operation_stage == "test_stage"
            assert metrics.start_time is not None
            assert not metrics.error_occurred
            
            # Simulate some work
            await asyncio.sleep(0.01)
        
        # Check that metrics were finalized
        assert metrics.end_time is not None
        assert metrics.duration_seconds is not None
        assert metrics.duration_seconds > 0
        
        # Check that operation was moved to completed
        completed = tracker.get_completed_operations()
        assert len(completed) == 1
        assert completed[0] == metrics
    
    @pytest.mark.asyncio
    async def test_track_operation_error(self, tracker, mock_export_context):
        """Test operation tracking with error."""
        with pytest.raises(ValueError):
            async with tracker.track_operation(mock_export_context, "test_stage") as metrics:
                raise ValueError("Test error")
        
        # Check that error was recorded
        assert metrics.error_occurred
        assert metrics.error_message == "Test error"
        
        # Check that operation was still moved to completed
        completed = tracker.get_completed_operations()
        assert len(completed) == 1
        assert completed[0].error_occurred
    
    def test_record_progress(self, tracker, mock_export_context):
        """Test progress recording."""
        # Start an operation
        operation_key = f"{mock_export_context.job_id}_{mock_export_context.request_id}_test_stage"
        metrics = ExportPerformanceMetrics(
            job_id=mock_export_context.job_id,
            request_id=str(mock_export_context.request_id),
            format=mock_export_context.format,
            operation_stage="test_stage",
            start_time=datetime.now(timezone.utc)
        )
        tracker._active_operations[operation_key] = metrics
        
        # Record progress
        tracker.record_progress(
            request_id=str(mock_export_context.request_id),
            rows_processed=1000000,  # 1M rows to trigger progress logging
            data_volume_mb=100.0,
            additional_context={"test": "context"}
        )
        
        # Check that progress was recorded
        assert metrics.total_rows_processed == 1000000
        assert metrics.data_volume_mb == 100.0
        assert len(metrics.progress_indicators) == 1
        
        progress = metrics.progress_indicators[0]
        assert progress["rows_processed"] == 1000000
        assert progress["data_volume_mb"] == 100.0
        assert "test" in progress
    
    def test_get_performance_summary(self, tracker):
        """Test performance summary generation."""
        # Add some mock completed operations
        successful_op = ExportPerformanceMetrics(
            job_id=1,
            request_id="req-1",
            format=ExportFormat.CSV,
            operation_stage="test",
            start_time=datetime.now(timezone.utc),
            end_time=datetime.now(timezone.utc),
            duration_seconds=10.0,
            total_rows_processed=1000,
            data_volume_mb=10.0,
            error_occurred=False
        )
        
        failed_op = ExportPerformanceMetrics(
            job_id=2,
            request_id="req-2",
            format=ExportFormat.EXCEL,
            operation_stage="test",
            start_time=datetime.now(timezone.utc),
            end_time=datetime.now(timezone.utc),
            error_occurred=True,
            error_message="Test error"
        )
        
        tracker._completed_operations = [successful_op, failed_op]
        
        summary = tracker.get_performance_summary()
        
        assert summary["completed_operations"] == 2
        assert summary["successful_operations"] == 1
        assert summary["failed_operations"] == 1
        assert summary["success_rate"] == 0.5
        assert "avg_duration_seconds" in summary
        assert "total_rows_processed" in summary


class TestExportMonitoringIntegration:
    """Test the ExportMonitoringIntegration class."""
    
    @pytest.fixture
    def integration(self):
        """Create a fresh integration instance for testing."""
        return ExportMonitoringIntegration()
    
    @pytest.fixture
    def mock_metrics(self):
        """Create mock performance metrics for testing."""
        return ExportPerformanceMetrics(
            job_id=123,
            request_id="test-request-123",
            format=ExportFormat.CSV,
            operation_stage="test_stage",
            start_time=datetime.now(timezone.utc),
            end_time=datetime.now(timezone.utc),
            duration_seconds=10.0,
            total_rows_processed=1000,
            data_volume_mb=10.0,
            memory_usage_start_mb=100.0,
            memory_usage_peak_mb=120.0,
            memory_usage_end_mb=110.0,
            memory_delta_mb=10.0,
            table_name="test.table",
            database_name="test"
        )
    
    @pytest.mark.asyncio
    @patch('magic_gateway.export.monitoring.integration.LogsHandler')
    async def test_log_export_metrics(self, mock_logs_handler, integration, mock_metrics):
        """Test logging export metrics to database."""
        mock_logs_handler.execute_command = AsyncMock()
        
        await integration.log_export_metrics(mock_metrics)
        
        # Verify that database insert was called
        mock_logs_handler.execute_command.assert_called_once()
        call_args = mock_logs_handler.execute_command.call_args
        
        # Check that the query contains the expected table name
        query = call_args[0][0]
        assert "api.export_performance_metrics" in query
        
        # Check that the data contains expected fields
        data = call_args[0][1]
        assert data["job_id"] == 123
        assert data["request_id"] == "test-request-123"
        assert data["export_format"] == "csv"
        assert data["duration_seconds"] == 10.0
        assert data["total_rows_processed"] == 1000
        assert data["success"] is True
    
    @pytest.mark.asyncio
    @patch('magic_gateway.export.monitoring.integration.LogsHandler')
    async def test_log_export_error(self, mock_logs_handler, integration):
        """Test logging export errors to database."""
        mock_logs_handler.execute_command = AsyncMock()
        
        await integration.log_export_error(
            job_id=123,
            request_id="test-request-123",
            error_type="test_error",
            error_message="Test error message",
            error_context={"test": "context"},
            operation_stage="test_stage",
            recovery_suggestions=["Try again"]
        )
        
        # Verify that database insert was called
        mock_logs_handler.execute_command.assert_called_once()
        call_args = mock_logs_handler.execute_command.call_args
        
        # Check that the query contains the expected table name
        query = call_args[0][0]
        assert "api.export_error_logs" in query
        
        # Check that the data contains expected fields
        data = call_args[0][1]
        assert data["job_id"] == 123
        assert data["request_id"] == "test-request-123"
        assert data["error_type"] == "test_error"
        assert data["error_message"] == "Test error message"
    
    @pytest.mark.asyncio
    @patch('magic_gateway.export.monitoring.integration.LogsHandler')
    async def test_get_export_metrics_summary(self, mock_logs_handler, integration):
        """Test getting export metrics summary from database."""
        # Mock database response
        mock_results = [
            {
                "total_exports": 10,
                "successful_exports": 8,
                "failed_exports": 2,
                "avg_duration_seconds": 15.5,
                "avg_rows_processed": 50000,
                "avg_data_volume_mb": 25.0,
                "export_format": "csv",
                "operation_stage": "complete_export"
            }
        ]
        mock_logs_handler.execute_query = AsyncMock(return_value=(mock_results, None))
        
        summary = await integration.get_export_metrics_summary(hours=24)
        
        # Verify query was executed
        mock_logs_handler.execute_query.assert_called_once()
        
        # Check summary structure
        assert summary["time_period_hours"] == 24
        assert summary["overall_total_exports"] == 10
        assert summary["overall_successful_exports"] == 8
        assert summary["overall_failed_exports"] == 2
        assert summary["overall_success_rate"] == 80.0
        assert len(summary["metrics_by_format_and_stage"]) == 1
        
        format_metrics = summary["metrics_by_format_and_stage"][0]
        assert format_metrics["export_format"] == "csv"
        assert format_metrics["success_rate"] == 80.0


@pytest.mark.asyncio
async def test_global_instances():
    """Test that global instances are properly initialized."""
    # Test that global instances exist and are of correct type
    assert isinstance(export_performance_tracker, ExportPerformanceTracker)
    assert isinstance(export_monitoring_integration, ExportMonitoringIntegration)
    
    # Test basic functionality
    summary = export_performance_tracker.get_performance_summary()
    assert isinstance(summary, dict)
    assert "active_operations" in summary
    assert "completed_operations" in summary