"""
Integration service for export monitoring with existing infrastructure.

This module provides integration between export performance tracking and the
existing MagicGateway monitoring infrastructure, including database logging
and metrics collection.
"""

import json
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

from magic_gateway.core.logging_config import log
from magic_gateway.db.logs_handler import LogsHandler
from magic_gateway.export.monitoring.performance_tracker import (
    ExportPerformanceMetrics,
    export_performance_tracker
)
from magic_gateway.monitoring.service import ConnectionPoolMonitoringService


class ExportMonitoringIntegration:
    """
    Service for integrating export performance tracking with existing monitoring infrastructure.
    
    This class provides:
    - Database logging of export metrics
    - Integration with connection pool monitoring
    - Success/failure tracking
    - Detailed error context logging
    """
    
    def __init__(self, connection_pool_monitor: Optional[ConnectionPoolMonitoringService] = None):
        """
        Initialize the export monitoring integration.
        
        Args:
            connection_pool_monitor: Optional connection pool monitoring service
        """
        self.connection_pool_monitor = connection_pool_monitor
        self._export_metrics_table = "api.export_performance_metrics"
        self._export_errors_table = "api.export_error_logs"
    
    async def log_export_metrics(self, metrics: ExportPerformanceMetrics) -> None:
        """
        Log export performance metrics to the database.
        
        Args:
            metrics: Export performance metrics to log
        """
        try:
            # Prepare metrics data for database insertion
            metrics_data = {
                "timestamp": metrics.end_time or metrics.start_time,
                "job_id": metrics.job_id,
                "request_id": metrics.request_id,
                "export_format": metrics.format.value,
                "operation_stage": metrics.operation_stage,
                "duration_seconds": metrics.duration_seconds,
                "total_rows_processed": metrics.total_rows_processed,
                "data_volume_mb": metrics.data_volume_mb,
                "rows_per_second": metrics.rows_per_second,
                "mb_per_second": metrics.mb_per_second,
                "memory_usage_start_mb": metrics.memory_usage_start_mb,
                "memory_usage_peak_mb": metrics.memory_usage_peak_mb,
                "memory_usage_end_mb": metrics.memory_usage_end_mb,
                "memory_delta_mb": metrics.memory_delta_mb,
                "table_name": metrics.table_name,
                "database_name": metrics.database_name,
                "optimization_strategy": metrics.optimization_strategy,
                "connection_pool_type": metrics.connection_pool_type,
                "success": not metrics.error_occurred,
                "error_message": metrics.error_message,
                "progress_indicators_count": len(metrics.progress_indicators),
                "additional_metrics": json.dumps({
                    "progress_indicators": metrics.progress_indicators,
                    "start_time": metrics.start_time.isoformat(),
                    "end_time": metrics.end_time.isoformat() if metrics.end_time else None
                })
            }
            
            # Insert metrics into database
            await self._insert_export_metrics(metrics_data)
            
            log.debug(
                f"Logged export metrics to database for job {metrics.job_id} "
                f"(request: {metrics.request_id})"
            )
            
        except Exception as e:
            log.error(
                f"Failed to log export metrics to database for job {metrics.job_id}: {e}",
                exc_info=True
            )
    
    async def log_export_error(
        self,
        job_id: int,
        request_id: str,
        error_type: str,
        error_message: str,
        error_context: Dict[str, Any],
        operation_stage: str,
        recovery_suggestions: Optional[List[str]] = None
    ) -> None:
        """
        Log detailed export error information to the database.
        
        Args:
            job_id: Job ID where error occurred
            request_id: Request ID for tracking
            error_type: Type/category of the error
            error_message: Error message
            error_context: Additional error context
            operation_stage: Stage where error occurred
            recovery_suggestions: Optional recovery suggestions
        """
        try:
            error_data = {
                "timestamp": datetime.now(timezone.utc),
                "job_id": job_id,
                "request_id": request_id,
                "error_type": error_type,
                "error_message": error_message,
                "operation_stage": operation_stage,
                "error_context": json.dumps(error_context),
                "recovery_suggestions": json.dumps(recovery_suggestions or []),
                "connection_pool_status": await self._get_connection_pool_status()
            }
            
            await self._insert_export_error(error_data)
            
            log.debug(
                f"Logged export error to database for job {job_id} "
                f"(request: {request_id}, type: {error_type})"
            )
            
        except Exception as e:
            log.error(
                f"Failed to log export error to database for job {job_id}: {e}",
                exc_info=True
            )
    
    async def get_export_metrics_summary(
        self,
        hours: int = 24,
        job_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Get summary of export metrics from the database.
        
        Args:
            hours: Number of hours to look back
            job_id: Optional job ID filter
            
        Returns:
            Dictionary containing export metrics summary
        """
        try:
            # Build query conditions
            conditions = [f"timestamp >= NOW() - INTERVAL '{hours} hours'"]
            if job_id is not None:
                conditions.append(f"job_id = {job_id}")
            
            where_clause = " AND ".join(conditions)
            
            # Query export metrics
            query = f"""
                SELECT 
                    COUNT(*) as total_exports,
                    COUNT(CASE WHEN success THEN 1 END) as successful_exports,
                    COUNT(CASE WHEN NOT success THEN 1 END) as failed_exports,
                    AVG(duration_seconds) as avg_duration_seconds,
                    AVG(total_rows_processed) as avg_rows_processed,
                    AVG(data_volume_mb) as avg_data_volume_mb,
                    AVG(memory_delta_mb) as avg_memory_delta_mb,
                    SUM(total_rows_processed) as total_rows_processed,
                    SUM(data_volume_mb) as total_data_volume_mb,
                    export_format,
                    operation_stage
                FROM {self._export_metrics_table}
                WHERE {where_clause}
                GROUP BY export_format, operation_stage
                ORDER BY total_exports DESC
            """
            
            results, _ = await LogsHandler.execute_query(query)
            
            # Process results into summary format
            summary = {
                "time_period_hours": hours,
                "job_id_filter": job_id,
                "metrics_by_format_and_stage": []
            }
            
            total_exports = 0
            total_successful = 0
            total_failed = 0
            
            for row in results:
                format_stage_metrics = {
                    "export_format": row.get("export_format"),
                    "operation_stage": row.get("operation_stage"),
                    "total_exports": row.get("total_exports", 0),
                    "successful_exports": row.get("successful_exports", 0),
                    "failed_exports": row.get("failed_exports", 0),
                    "success_rate": (row.get("successful_exports", 0) / row.get("total_exports", 1)) * 100,
                    "avg_duration_seconds": row.get("avg_duration_seconds"),
                    "avg_rows_processed": row.get("avg_rows_processed"),
                    "avg_data_volume_mb": row.get("avg_data_volume_mb"),
                    "avg_memory_delta_mb": row.get("avg_memory_delta_mb"),
                    "total_rows_processed": row.get("total_rows_processed"),
                    "total_data_volume_mb": row.get("total_data_volume_mb")
                }
                summary["metrics_by_format_and_stage"].append(format_stage_metrics)
                
                total_exports += row.get("total_exports", 0)
                total_successful += row.get("successful_exports", 0)
                total_failed += row.get("failed_exports", 0)
            
            # Add overall summary
            summary.update({
                "overall_total_exports": total_exports,
                "overall_successful_exports": total_successful,
                "overall_failed_exports": total_failed,
                "overall_success_rate": (total_successful / total_exports * 100) if total_exports > 0 else 0
            })
            
            return summary
            
        except Exception as e:
            log.error(f"Failed to get export metrics summary: {e}", exc_info=True)
            return {
                "error": f"Failed to retrieve metrics summary: {str(e)}",
                "time_period_hours": hours,
                "job_id_filter": job_id
            }
    
    async def get_recent_export_errors(
        self,
        hours: int = 24,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Get recent export errors from the database.
        
        Args:
            hours: Number of hours to look back
            limit: Maximum number of errors to return
            
        Returns:
            List of recent export errors
        """
        try:
            query = f"""
                SELECT 
                    timestamp,
                    job_id,
                    request_id,
                    error_type,
                    error_message,
                    operation_stage,
                    error_context,
                    recovery_suggestions,
                    connection_pool_status
                FROM {self._export_errors_table}
                WHERE timestamp >= NOW() - INTERVAL '{hours} hours'
                ORDER BY timestamp DESC
                LIMIT {limit}
            """
            
            results, _ = await LogsHandler.execute_query(query)
            
            errors = []
            for row in results:
                error_info = {
                    "timestamp": row.get("timestamp"),
                    "job_id": row.get("job_id"),
                    "request_id": row.get("request_id"),
                    "error_type": row.get("error_type"),
                    "error_message": row.get("error_message"),
                    "operation_stage": row.get("operation_stage"),
                    "connection_pool_status": row.get("connection_pool_status")
                }
                
                # Parse JSON fields
                try:
                    if row.get("error_context"):
                        error_info["error_context"] = json.loads(row["error_context"])
                except:
                    error_info["error_context"] = row.get("error_context")
                
                try:
                    if row.get("recovery_suggestions"):
                        error_info["recovery_suggestions"] = json.loads(row["recovery_suggestions"])
                except:
                    error_info["recovery_suggestions"] = []
                
                errors.append(error_info)
            
            return errors
            
        except Exception as e:
            log.error(f"Failed to get recent export errors: {e}", exc_info=True)
            return []
    
    async def _insert_export_metrics(self, metrics_data: Dict[str, Any]) -> None:
        """Insert export metrics into the database."""
        # Try to insert with full schema first
        query_full = f"""
            INSERT INTO {self._export_metrics_table} (
                timestamp, job_id, request_id, export_format, operation_stage,
                duration_seconds, total_rows_processed, data_volume_mb,
                rows_per_second, mb_per_second, memory_usage_start_mb,
                memory_usage_peak_mb, memory_usage_end_mb, memory_delta_mb,
                table_name, database_name, optimization_strategy,
                connection_pool_type, success, error_message,
                progress_indicators_count, additional_metrics
            ) VALUES (
                %(timestamp)s, %(job_id)s, %(request_id)s, %(export_format)s, %(operation_stage)s,
                %(duration_seconds)s, %(total_rows_processed)s, %(data_volume_mb)s,
                %(rows_per_second)s, %(mb_per_second)s, %(memory_usage_start_mb)s,
                %(memory_usage_peak_mb)s, %(memory_usage_end_mb)s, %(memory_delta_mb)s,
                %(table_name)s, %(database_name)s, %(optimization_strategy)s,
                %(connection_pool_type)s, %(success)s, %(error_message)s,
                %(progress_indicators_count)s, %(additional_metrics)s
            )
        """
        
        try:
            await LogsHandler.execute_command(query_full, metrics_data)
        except Exception as e:
            # If table doesn't exist, create it
            if "does not exist" in str(e).lower():
                await self._create_export_metrics_table()
                await LogsHandler.execute_command(query_full, metrics_data)
            else:
                raise
    
    async def _insert_export_error(self, error_data: Dict[str, Any]) -> None:
        """Insert export error into the database."""
        query = f"""
            INSERT INTO {self._export_errors_table} (
                timestamp, job_id, request_id, error_type, error_message,
                operation_stage, error_context, recovery_suggestions,
                connection_pool_status
            ) VALUES (
                %(timestamp)s, %(job_id)s, %(request_id)s, %(error_type)s, %(error_message)s,
                %(operation_stage)s, %(error_context)s, %(recovery_suggestions)s,
                %(connection_pool_status)s
            )
        """
        
        try:
            await LogsHandler.execute_command(query, error_data)
        except Exception as e:
            # If table doesn't exist, create it
            if "does not exist" in str(e).lower():
                await self._create_export_errors_table()
                await LogsHandler.execute_command(query, error_data)
            else:
                raise
    
    async def _create_export_metrics_table(self) -> None:
        """Create the export metrics table if it doesn't exist."""
        create_table_query = f"""
            CREATE TABLE IF NOT EXISTS {self._export_metrics_table} (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
                job_id INTEGER NOT NULL,
                request_id VARCHAR(255) NOT NULL,
                export_format VARCHAR(50) NOT NULL,
                operation_stage VARCHAR(100) NOT NULL,
                duration_seconds DOUBLE PRECISION,
                total_rows_processed BIGINT DEFAULT 0,
                data_volume_mb DOUBLE PRECISION DEFAULT 0,
                rows_per_second DOUBLE PRECISION,
                mb_per_second DOUBLE PRECISION,
                memory_usage_start_mb DOUBLE PRECISION DEFAULT 0,
                memory_usage_peak_mb DOUBLE PRECISION DEFAULT 0,
                memory_usage_end_mb DOUBLE PRECISION DEFAULT 0,
                memory_delta_mb DOUBLE PRECISION DEFAULT 0,
                table_name VARCHAR(255),
                database_name VARCHAR(255),
                optimization_strategy VARCHAR(100),
                connection_pool_type VARCHAR(100),
                success BOOLEAN NOT NULL DEFAULT TRUE,
                error_message TEXT,
                progress_indicators_count INTEGER DEFAULT 0,
                additional_metrics JSONB,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            
            CREATE INDEX IF NOT EXISTS idx_export_metrics_timestamp ON {self._export_metrics_table} (timestamp);
            CREATE INDEX IF NOT EXISTS idx_export_metrics_job_id ON {self._export_metrics_table} (job_id);
            CREATE INDEX IF NOT EXISTS idx_export_metrics_request_id ON {self._export_metrics_table} (request_id);
            CREATE INDEX IF NOT EXISTS idx_export_metrics_success ON {self._export_metrics_table} (success);
        """
        
        await LogsHandler.execute_command(create_table_query)
        log.info(f"Created export metrics table: {self._export_metrics_table}")
    
    async def _create_export_errors_table(self) -> None:
        """Create the export errors table if it doesn't exist."""
        create_table_query = f"""
            CREATE TABLE IF NOT EXISTS {self._export_errors_table} (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
                job_id INTEGER NOT NULL,
                request_id VARCHAR(255) NOT NULL,
                error_type VARCHAR(100) NOT NULL,
                error_message TEXT NOT NULL,
                operation_stage VARCHAR(100) NOT NULL,
                error_context JSONB,
                recovery_suggestions JSONB,
                connection_pool_status TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );
            
            CREATE INDEX IF NOT EXISTS idx_export_errors_timestamp ON {self._export_errors_table} (timestamp);
            CREATE INDEX IF NOT EXISTS idx_export_errors_job_id ON {self._export_errors_table} (job_id);
            CREATE INDEX IF NOT EXISTS idx_export_errors_request_id ON {self._export_errors_table} (request_id);
            CREATE INDEX IF NOT EXISTS idx_export_errors_type ON {self._export_errors_table} (error_type);
        """
        
        await LogsHandler.execute_command(create_table_query)
        log.info(f"Created export errors table: {self._export_errors_table}")
    
    async def _get_connection_pool_status(self) -> Optional[str]:
        """Get current connection pool status for error context."""
        try:
            if self.connection_pool_monitor:
                status = self.connection_pool_monitor.get_connection_pool_status()
                return json.dumps({k: v.model_dump() for k, v in status.items()})
            return None
        except Exception as e:
            log.warning(f"Failed to get connection pool status: {e}")
            return f"Error retrieving status: {str(e)}"


# Global export monitoring integration instance
export_monitoring_integration = ExportMonitoringIntegration()