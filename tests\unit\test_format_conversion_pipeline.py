"""
Unit tests for the FormatConversionPipeline class.

Tests the format conversion pipeline functionality including CSV conversion,
error handling, and resource management.
"""

import asyncio
import tempfile
import os
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import AsyncGenerator

from magic_gateway.export.pipeline.format_conversion import FormatConversionPipeline
from magic_gateway.export.models import ExportFormat, ConversionOptions
from magic_gateway.export.exceptions import ExportError
from magic_gateway.export.resources.manager import ExportResourceManager


class TestFormatConversionPipeline:
    """Test cases for FormatConversionPipeline."""
    
    @pytest.fixture
    def pipeline(self):
        """Create a FormatConversionPipeline instance for testing."""
        return FormatConversionPipeline()
    
    @pytest.fixture
    def sample_metadata(self):
        """Sample metadata for testing."""
        return {
            "filename": "test_export",
            "request_id": "test-request-123",
            "job_info": {"job_id": 123, "name": "Test Job"}
        }
    
    @pytest.fixture
    def sample_options(self):
        """Sample conversion options for testing."""
        return ConversionOptions(
            separate_periods=False,
            horizontal_facts=False,
            
        )
    
    async def create_sample_parquet_stream(self) -> AsyncGenerator[bytes, None]:
        """Create a sample parquet stream for testing."""
        # This would normally be actual parquet data
        sample_data = [
            b"parquet_header_data",
            b"parquet_row_data_1",
            b"parquet_row_data_2",
            b"parquet_footer_data"
        ]
        
        for chunk in sample_data:
            yield chunk
    
    @pytest.mark.asyncio
    async def test_pipeline_initialization(self, pipeline):
        """Test pipeline initialization."""
        assert isinstance(pipeline, FormatConversionPipeline)
        assert pipeline.temp_files == []
    
    @pytest.mark.asyncio
    async def test_unsupported_format_raises_error(self, pipeline, sample_metadata, sample_options):
        """Test that unsupported format raises ExportError."""
        # Create a mock format that doesn't exist
        with patch('magic_gateway.export.models.ExportFormat') as mock_format:
            mock_format.value = "unsupported_format"
            
            parquet_stream = self.create_sample_parquet_stream()
            
            with pytest.raises(ExportError) as exc_info:
                await pipeline.convert_stream(
                    parquet_stream=parquet_stream,
                    target_format=mock_format,
                    metadata=sample_metadata,
                    options=sample_options
                )
            
            assert exc_info.value.error_type == "unsupported_format"
            assert "unsupported_format" in exc_info.value.message.lower()
    
    @pytest.mark.asyncio
    async def test_save_parquet_stream(self, pipeline):
        """Test saving parquet stream to temporary file."""
        parquet_stream = self.create_sample_parquet_stream()
        
        temp_path = await pipeline._save_parquet_stream(parquet_stream, "test-request-123")
        
        # Verify file was created
        assert os.path.exists(temp_path)
        assert str(temp_path) in pipeline.temp_files
        assert str(temp_path).endswith('.parquet')
        
        # Verify content was written
        with open(temp_path, 'rb') as f:
            content = f.read()
            assert b"parquet_header_data" in content
            assert b"parquet_row_data_1" in content
        
        # Cleanup
        os.unlink(temp_path)
    
    @pytest.mark.asyncio
    async def test_save_parquet_stream_error_handling(self, pipeline):
        """Test error handling when saving parquet stream fails."""
        async def failing_stream():
            yield b"some_data"
            raise Exception("Stream error")
        
        with pytest.raises(ExportError) as exc_info:
            await pipeline._save_parquet_stream(failing_stream())
        
        assert exc_info.value.error_type == "stream_save_failed"
        assert "Stream error" in exc_info.value.message
    





    @pytest.mark.asyncio
    async def test_convert_stream_excel_integration(self, pipeline, sample_metadata):
        """Test full Excel conversion integration through convert_stream."""
        with patch('magic_gateway.export.pipeline.format_conversion.StreamingExcelWriter') as mock_excel_writer_class:
            with patch('magic_gateway.utils.parquet_processor.get_parquet_file_info') as mock_file_info:
                # Mock Excel writer
                mock_excel_writer = MagicMock()
                mock_excel_writer.save.return_value = "/tmp/integration_test.xlsx"
                mock_excel_writer_class.return_value = mock_excel_writer
                
                # Mock file info
                mock_file_info.return_value = {
                    "num_rows": 1000,
                    "num_columns": 5,
                    "file_size_bytes": 1024 * 1024  # 1MB
                }
                
                # Create sample parquet stream
                async def sample_parquet_stream():
                    yield b"parquet_header"
                    yield b"parquet_data_chunk_1"
                    yield b"parquet_data_chunk_2"
                
                options = ConversionOptions()
                
                # Convert to Excel
                response = await pipeline.convert_stream(
                    parquet_stream=sample_parquet_stream(),
                    target_format=ExportFormat.EXCEL,
                    metadata=sample_metadata,
                    options=options
                )
                
                # Verify response
                assert response.media_type == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                assert "excel_test.xlsx" in response.headers["Content-Disposition"]
                
                # Verify Excel writer was used
                mock_excel_writer_class.assert_called_once_with("excel_test", horizontal_facts=False)
                mock_excel_writer.save.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_convert_stream_excel_facts_integration(self, pipeline, sample_metadata):
        """Test full Excel Facts conversion integration through convert_stream."""
        with patch('magic_gateway.export.pipeline.format_conversion.StreamingExcelWriter') as mock_excel_writer_class:
            with patch('magic_gateway.utils.parquet_processor.get_parquet_file_info') as mock_file_info:
                # Mock Excel writer
                mock_excel_writer = MagicMock()
                mock_excel_writer.save.return_value = "/tmp/facts_integration_test.xlsx"
                mock_excel_writer_class.return_value = mock_excel_writer
                
                # Mock file info
                mock_file_info.return_value = {
                    "num_rows": 500,
                    "num_columns": 8,
                    "file_size_bytes": 512 * 1024  # 512KB
                }
                
                # Create sample parquet stream
                async def sample_parquet_stream():
                    yield b"facts_parquet_header"
                    yield b"facts_parquet_data"
                
                options = ConversionOptions(horizontal_facts=True)
                
                # Convert to Excel Facts
                response = await pipeline.convert_stream(
                    parquet_stream=sample_parquet_stream(),
                    target_format=ExportFormat.EXCEL_FACTS,
                    metadata=sample_metadata,
                    options=options
                )
                
                # Verify response
                assert response.media_type == "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                assert "excel_test.xlsx" in response.headers["Content-Disposition"]
                
                # Verify Excel writer was created with horizontal_facts=True
                mock_excel_writer_class.assert_called_once_with("excel_test", horizontal_facts=True)
                mock_excel_writer.save.assert_called_once()


class TestFormatConversionPipelineParquet:
    """Specific tests for Parquet direct export functionality."""
    
    @pytest.fixture
    def pipeline(self):
        """Create a FormatConversionPipeline instance for testing."""
        return FormatConversionPipeline()
    
    @pytest.fixture
    def sample_metadata(self):
        """Sample metadata for testing."""
        return {
            "filename": "parquet_test",
            "request_id": "parquet-test-123",
            "archive_parquet": True
        }
    
    @pytest.mark.asyncio
    async def test_parquet_direct_with_archiving(self, pipeline, sample_metadata):
        """Test parquet direct export with archiving enabled."""
        # Create a temporary parquet file with some content
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_file:
            test_content = b"mock_parquet_file_content_for_archiving"
            temp_file.write(test_content)
            temp_path = temp_file.name
        
        try:
            options = ConversionOptions()
            response = await pipeline._convert_to_parquet_direct(temp_path, sample_metadata, options)
            
            # Verify response properties for ZIP file
            assert response.media_type == "application/zip"
            assert "parquet_test.zip" in response.headers["Content-Disposition"]
            assert "no-cache" in response.headers["Cache-Control"]
            
            # Verify content by consuming the response
            content_chunks = []
            async for chunk in response.body_iterator:
                content_chunks.append(chunk)
            
            full_content = b''.join(content_chunks)
            
            # Verify it's a valid ZIP file
            import zipfile
            import io
            
            with zipfile.ZipFile(io.BytesIO(full_content), 'r') as zipf:
                # Should contain one file
                assert len(zipf.namelist()) == 1
                assert zipf.namelist()[0] == "parquet_test.parquet"
                
                # Extract and verify content
                extracted_content = zipf.read("parquet_test.parquet")
                assert extracted_content == test_content
            
        finally:
            # Cleanup
            if os.path.exists(temp_path):
                os.unlink(temp_path)
    
    @pytest.mark.asyncio
    async def test_parquet_direct_without_archiving(self, pipeline):
        """Test parquet direct export without archiving."""
        # Create a temporary parquet file with some content
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_file:
            test_content = b"mock_parquet_file_content_no_archive"
            temp_file.write(test_content)
            temp_path = temp_file.name
        
        try:
            metadata = {
                "filename": "direct_parquet",
                "request_id": "direct-test-123",
                "archive_parquet": False  # Disable archiving
            }
            options = ConversionOptions()
            response = await pipeline._convert_to_parquet_direct(temp_path, metadata, options)
            
            # Verify response properties for direct parquet file
            assert response.media_type == "application/octet-stream"
            assert "direct_parquet.parquet" in response.headers["Content-Disposition"]
            assert "no-cache" in response.headers["Cache-Control"]
            
            # Verify content by consuming the response
            content_chunks = []
            async for chunk in response.body_iterator:
                content_chunks.append(chunk)
            
            full_content = b''.join(content_chunks)
            assert full_content == test_content
            
        finally:
            # Cleanup
            if os.path.exists(temp_path):
                os.unlink(temp_path)
    
    @pytest.mark.asyncio
    async def test_create_parquet_archive_success(self, pipeline):
        """Test successful creation of parquet archive."""
        # Create a temporary parquet file
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_file:
            test_content = b"test_parquet_content_for_archive"
            temp_file.write(test_content)
            temp_path = temp_file.name
        
        try:
            archive_path = await pipeline._create_parquet_archive(temp_path, "test_archive")
            
            # Verify archive was created
            assert os.path.exists(archive_path)
            assert archive_path.endswith('.zip')
            
            # Verify archive contents
            import zipfile
            with zipfile.ZipFile(archive_path, 'r') as zipf:
                assert len(zipf.namelist()) == 1
                assert zipf.namelist()[0] == "test_archive.parquet"
                
                extracted_content = zipf.read("test_archive.parquet")
                assert extracted_content == test_content
            
            # Cleanup archive
            os.unlink(archive_path)
            
        finally:
            # Cleanup original file
            if os.path.exists(temp_path):
                os.unlink(temp_path)
    
    @pytest.mark.asyncio
    async def test_create_parquet_archive_error_handling(self, pipeline):
        """Test error handling in archive creation."""
        # Try to archive a non-existent file
        non_existent_path = "/path/that/does/not/exist.parquet"
        
        with pytest.raises(ExportError) as exc_info:
            await pipeline._create_parquet_archive(non_existent_path, "test_error")
        
        assert exc_info.value.error_type == "archive_creation_failed"
        assert "Failed to create parquet archive" in exc_info.value.message
    
    @pytest.mark.asyncio
    async def test_convert_stream_parquet_integration(self, pipeline):
        """Test full parquet conversion integration through convert_stream."""
        # Create sample parquet stream
        async def sample_parquet_stream():
            yield b"parquet_header_data"
            yield b"parquet_row_data_1"
            yield b"parquet_row_data_2"
        
        metadata = {
            "filename": "integration_parquet",
            "request_id": "integration-test-123",
            "archive_parquet": True
        }
        options = ConversionOptions()
        
        # Convert to parquet (should be pass-through with archiving)
        response = await pipeline.convert_stream(
            parquet_stream=sample_parquet_stream(),
            target_format=ExportFormat.PARQUET,
            metadata=metadata,
            options=options
        )
        
        # Verify response
        assert response.media_type == "application/zip"
        assert "integration_parquet.zip" in response.headers["Content-Disposition"]
        
        # Verify content
        content_chunks = []
        async for chunk in response.body_iterator:
            content_chunks.append(chunk)
        
        full_content = b''.join(content_chunks)
        
        # Should be a valid ZIP file
        import zipfile
        import io
        
        with zipfile.ZipFile(io.BytesIO(full_content), 'r') as zipf:
            assert len(zipf.namelist()) == 1
            assert zipf.namelist()[0] == "integration_parquet.parquet"
            
            # Extract and verify it contains the original stream data
            extracted_content = zipf.read("integration_parquet.parquet")
            expected_content = b"parquet_header_dataparquet_row_data_1parquet_row_data_2"
            assert extracted_content == expected_content
    
    @pytest.mark.asyncio
    async def test_parquet_archiving_with_large_file(self, pipeline):
        """Test parquet archiving with a larger file to verify streaming."""
        # Create a larger temporary parquet file
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_file:
            # Write 100KB of test data
            test_chunk = b"A" * 1024  # 1KB chunk
            for _ in range(100):  # 100KB total
                temp_file.write(test_chunk)
            temp_path = temp_file.name
        
        try:
            metadata = {
                "filename": "large_parquet",
                "request_id": "large-test-123",
                "archive_parquet": True
            }
            options = ConversionOptions()
            response = await pipeline._convert_to_parquet_direct(temp_path, metadata, options)
            
            # Verify response properties
            assert response.media_type == "application/zip"
            assert "large_parquet.zip" in response.headers["Content-Disposition"]
            
            # Consume response and verify it's streamable
            total_size = 0
            chunk_count = 0
            async for chunk in response.body_iterator:
                total_size += len(chunk)
                chunk_count += 1
            
            # Should have received multiple chunks for streaming
            assert chunk_count > 1
            assert total_size > 0
            
            log.info(f"Large parquet archive: {total_size} bytes in {chunk_count} chunks")
            
        finally:
            # Cleanup
            if os.path.exists(temp_path):
                os.unlink(temp_path)
    
    @pytest.mark.asyncio
    async def test_parquet_default_archiving_behavior(self, pipeline):
        """Test that parquet files are archived by default."""
        # Create a temporary parquet file
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_file:
            test_content = b"default_behavior_test_content"
            temp_file.write(test_content)
            temp_path = temp_file.name
        
        try:
            # Don't specify archive_parquet in metadata - should default to True
            metadata = {
                "filename": "default_archive",
                "request_id": "default-test-123"
            }
            options = ConversionOptions()
            response = await pipeline._convert_to_parquet_direct(temp_path, metadata, options)
            
            # Should be archived by default
            assert response.media_type == "application/zip"
            assert "default_archive.zip" in response.headers["Content-Disposition"]
            
        finally:
            # Cleanup
            if os.path.exists(temp_path):
                os.unlink(temp_path)


class TestFormatConversionPipelineResourceManager:
    """Tests for FormatConversionPipeline integration with ExportResourceManager."""
    
    @pytest.fixture
    def mock_resource_manager(self):
        """Create a mock ExportResourceManager for testing."""
        manager = AsyncMock(spec=ExportResourceManager)
        manager.register_temp_file_from_manager.return_value = "test-resource-id"
        manager.get_temp_file_path.return_value = "/tmp/test_file.parquet"
        return manager
    
    @pytest.fixture
    def pipeline_with_resource_manager(self, mock_resource_manager):
        """Create a FormatConversionPipeline with resource manager."""
        return FormatConversionPipeline(resource_manager=mock_resource_manager)
    
    @pytest.fixture
    def sample_metadata(self):
        """Sample metadata for testing."""
        return {
            "filename": "test_export",
            "request_id": "test-request-123",
            "job_info": {"job_id": 123, "name": "Test Job"}
        }
    
    @pytest.fixture
    def sample_options(self):
        """Sample conversion options for testing."""
        return ConversionOptions(
            separate_periods=False,
            horizontal_facts=False,
            
        )
    
    async def create_sample_parquet_stream(self) -> AsyncGenerator[bytes, None]:
        """Create a sample parquet stream for testing."""
        sample_data = [
            b"parquet_header_data",
            b"parquet_row_data_1",
            b"parquet_row_data_2",
            b"parquet_footer_data"
        ]
        for chunk in sample_data:
            yield chunk
    
    @pytest.mark.asyncio
    async def test_pipeline_initialization_with_resource_manager(self, mock_resource_manager):
        """Test pipeline initialization with resource manager."""
        pipeline = FormatConversionPipeline(resource_manager=mock_resource_manager)
        assert pipeline.resource_manager is mock_resource_manager
        assert pipeline.temp_files == []  # Should still have temp_files for backward compatibility
    
    @pytest.mark.asyncio
    async def test_pipeline_initialization_without_resource_manager(self):
        """Test pipeline initialization without resource manager (legacy mode)."""
        pipeline = FormatConversionPipeline()
        assert pipeline.resource_manager is None
        assert pipeline.temp_files == []
    
    @pytest.mark.asyncio
    async def test_save_parquet_stream_with_resource_manager(self, pipeline_with_resource_manager, mock_resource_manager):
        """Test saving parquet stream using resource manager."""
        parquet_stream = self.create_sample_parquet_stream()
        
        result_path = await pipeline_with_resource_manager._save_parquet_stream(parquet_stream)
        
        # Verify resource manager was called correctly
        mock_resource_manager.register_temp_file_from_manager.assert_called_once()
        call_args = mock_resource_manager.register_temp_file_from_manager.call_args
        assert call_args[1]['extension'] == "parquet"
        assert call_args[1]['prefix'] == "export_parquet_"
        assert isinstance(call_args[1]['content'], bytes)
        
        # Verify file path was retrieved
        mock_resource_manager.get_temp_file_path.assert_called_once_with("test-resource-id")
        assert result_path == "/tmp/test_file.parquet"
        
        # Verify temp_files is not used when resource manager is available
        assert len(pipeline_with_resource_manager.temp_files) == 0
    
    @pytest.mark.asyncio
    async def test_save_parquet_stream_legacy_mode(self):
        """Test saving parquet stream in legacy mode (without resource manager)."""
        pipeline = FormatConversionPipeline()  # No resource manager
        parquet_stream = self.create_sample_parquet_stream()
        
        with patch('tempfile.NamedTemporaryFile') as mock_tempfile:
            mock_file = MagicMock()
            mock_file.name = "/tmp/legacy_temp.parquet"
            mock_tempfile.return_value = mock_file
            
            result_path = await pipeline._save_parquet_stream(parquet_stream)
            
            # Verify tempfile was used
            mock_tempfile.assert_called_once_with(delete=False, suffix='.parquet')
            mock_file.close.assert_called()
            
            # Verify temp_files tracking
            assert result_path == "/tmp/legacy_temp.parquet"
            assert "/tmp/legacy_temp.parquet" in pipeline.temp_files
    
    @pytest.mark.asyncio
    async def test_create_parquet_archive_with_resource_manager(self, pipeline_with_resource_manager, mock_resource_manager):
        """Test creating parquet archive using resource manager."""
        # Create a temporary parquet file for testing
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_file:
            temp_file.write(b"test parquet data")
            parquet_path = temp_file.name
        
        try:
            # Mock resource manager to return a ZIP file path
            mock_resource_manager.get_temp_file_path.return_value = "/tmp/test_archive.zip"
            
            result_path = await pipeline_with_resource_manager._create_parquet_archive(parquet_path, "test_export")
            
            # Verify resource manager was called correctly
            mock_resource_manager.register_temp_file_from_manager.assert_called_once()
            call_args = mock_resource_manager.register_temp_file_from_manager.call_args
            assert call_args[1]['extension'] == "zip"
            assert call_args[1]['prefix'] == "export_archive_"
            assert isinstance(call_args[1]['content'], bytes)
            
            # Verify file path was retrieved
            mock_resource_manager.get_temp_file_path.assert_called_once_with("test-resource-id")
            assert result_path == "/tmp/test_archive.zip"
            
        finally:
            # Clean up temporary file
            if os.path.exists(parquet_path):
                os.unlink(parquet_path)
    
    @pytest.mark.asyncio
    async def test_create_parquet_archive_legacy_mode(self):
        """Test creating parquet archive in legacy mode."""
        pipeline = FormatConversionPipeline()  # No resource manager
        
        # Create a temporary parquet file for testing
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_file:
            temp_file.write(b"test parquet data")
            parquet_path = temp_file.name
        
        try:
            with patch('tempfile.NamedTemporaryFile') as mock_tempfile:
                mock_file = MagicMock()
                mock_file.name = "/tmp/legacy_archive.zip"
                mock_tempfile.return_value = mock_file
                
                result_path = await pipeline._create_parquet_archive(parquet_path, "test_export")
                
                # Verify tempfile was used
                mock_tempfile.assert_called_once_with(delete=False, suffix='.zip')
                mock_file.close.assert_called()
                assert result_path == "/tmp/legacy_archive.zip"
                
        finally:
            # Clean up temporary file
            if os.path.exists(parquet_path):
                os.unlink(parquet_path)
    
    @pytest.mark.asyncio
    async def test_resource_manager_error_handling(self, mock_resource_manager):
        """Test error handling when resource manager operations fail."""
        pipeline = FormatConversionPipeline(resource_manager=mock_resource_manager)
        
        # Mock resource manager to raise an error
        mock_resource_manager.register_temp_file_from_manager.side_effect = OSError("Resource manager failed")
        
        parquet_stream = self.create_sample_parquet_stream()
        
        with pytest.raises(ExportError) as exc_info:
            await pipeline._save_parquet_stream(parquet_stream)
        
        assert exc_info.value.error_type == "stream_save_failed"
        assert "Resource manager failed" in exc_info.value.message
        assert "Check resource manager status" in exc_info.value.recovery_suggestions
    
    @pytest.mark.asyncio
    async def test_resource_manager_file_path_error(self, mock_resource_manager):
        """Test error handling when resource manager can't provide file path."""
        pipeline = FormatConversionPipeline(resource_manager=mock_resource_manager)
        
        # Mock resource manager to return None for file path
        mock_resource_manager.get_temp_file_path.return_value = None
        
        parquet_stream = self.create_sample_parquet_stream()
        
        with pytest.raises(ExportError) as exc_info:
            await pipeline._save_parquet_stream(parquet_stream)
        
        assert exc_info.value.error_type == "stream_save_failed"
        assert "Failed to get file path for resource_id" in exc_info.value.message
    
    @pytest.mark.asyncio
    async def test_convert_stream_with_resource_manager_integration(self, pipeline_with_resource_manager, mock_resource_manager, sample_metadata, sample_options):
        """Test full convert_stream integration with resource manager."""
        parquet_stream = self.create_sample_parquet_stream()
        
        # Mock the CSV conversion function
        with patch('magic_gateway.export.pipeline.format_conversion.convert_parquet_to_csv_stream') as mock_csv_convert:
            mock_csv_convert.return_value = AsyncMock()
            mock_csv_convert.return_value.__aiter__.return_value = iter([b"csv,data\n", b"row1,value1\n"])
            
            response = await pipeline_with_resource_manager.convert_stream(
                parquet_stream=parquet_stream,
                target_format=ExportFormat.CSV,
                metadata=sample_metadata,
                options=sample_options
            )
            
            # Verify resource manager was used for parquet file
            mock_resource_manager.register_temp_file_from_manager.assert_called_once()
            mock_resource_manager.get_temp_file_path.assert_called_once()
            
            # Verify response is created
            assert response is not None
            assert response.media_type == "text/csv"
    
    @pytest.mark.asyncio
    async def test_parquet_archiving_with_resource_manager(self, pipeline_with_resource_manager, mock_resource_manager, sample_metadata, sample_options):
        """Test parquet archiving integration with resource manager."""
        parquet_stream = self.create_sample_parquet_stream()
        
        # Mock resource manager to return different paths for parquet and archive
        def mock_get_path(resource_id):
            if "parquet" in resource_id:
                return "/tmp/test_file.parquet"
            else:
                return "/tmp/test_archive.zip"
        
        mock_resource_manager.get_temp_file_path.side_effect = mock_get_path
        
        # Enable archiving in metadata
        sample_metadata["archive_parquet"] = True
        
        response = await pipeline_with_resource_manager.convert_stream(
            parquet_stream=parquet_stream,
            target_format=ExportFormat.PARQUET,
            metadata=sample_metadata,
            options=sample_options
        )
        
        # Verify both parquet file and archive were created via resource manager
        assert mock_resource_manager.register_temp_file_from_manager.call_count == 2
        
        # Verify response is for ZIP file
        assert response is not None
        assert response.media_type == "application/zip"
    
    @pytest.mark.asyncio
    async def test_temp_files_not_tracked_with_resource_manager(self, pipeline_with_resource_manager, mock_resource_manager):
        """Test that temp_files list is not used when resource manager is available."""
        # Create a temporary parquet file for testing
        with tempfile.NamedTemporaryFile(suffix='.parquet', delete=False) as temp_file:
            temp_file.write(b"test parquet data")
            parquet_path = temp_file.name
        
        try:
            # Mock resource manager to return a ZIP file path
            mock_resource_manager.get_temp_file_path.return_value = "/tmp/test_archive.zip"
            
            # Test archiving (which would normally add to temp_files in legacy mode)
            await pipeline_with_resource_manager._create_parquet_archive(parquet_path, "test_export")
            
            # Verify temp_files is still empty (resource manager handles cleanup)
            assert len(pipeline_with_resource_manager.temp_files) == 0
            
        finally:
            # Clean up temporary file
            if os.path.exists(parquet_path):
                os.unlink(parquet_path)

