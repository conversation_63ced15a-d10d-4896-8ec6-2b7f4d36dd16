"""
Format conversion pipeline for the export optimization system.

This module provides the FormatConversionPipeline class that handles
conversion of parquet streams to different export formats.
"""

import asyncio
import csv
import io
import tempfile
import os
from typing import AsyncGenerator, Dict, Any, Optional, List, TYPE_CHECKING
from fastapi.responses import StreamingResponse
from starlette.background import BackgroundTask

from magic_gateway.core.logging_config import log
from magic_gateway.export.models import ExportFormat, ConversionOptions
from magic_gateway.export.exceptions import ExportError
from magic_gateway.utils.parquet_processor import (
    read_parquet_file_async_streaming,
    convert_parquet_to_csv_stream,
    cleanup_parquet_file
)
from magic_gateway.utils.streaming_excel_writer import StreamingExcelWriter

if TYPE_CHECKING:
    from magic_gateway.export.resources.manager import ExportResourceManager


class FormatConversionPipeline:
    """
    Unified pipeline for converting parquet streams to different export formats.
    
    This class provides a consistent interface for format conversion operations,
    supporting streaming conversions to minimize memory usage.
    """
    
    def __init__(self, resource_manager: Optional["ExportResourceManager"] = None):
        """
        Initialize the format conversion pipeline.
        
        Args:
            resource_manager: Optional ExportResourceManager for temporary file management.
                            If None, will use manual cleanup (legacy behavior).
        """
        self.resource_manager = resource_manager
        self.temp_files: List[str] = []  # Keep for backward compatibility when no resource_manager
        
    async def convert_stream(
        self,
        parquet_stream: AsyncGenerator[bytes, None],
        target_format: ExportFormat,
        metadata: Dict[str, Any],
        options: ConversionOptions,
        connection_manager=None
    ) -> StreamingResponse:
        """
        Convert a parquet stream to the target format.
        
        Args:
            parquet_stream: Async generator yielding parquet data bytes
            target_format: Target export format
            metadata: Export metadata including filename, job info, etc.
            options: Conversion options
            
        Returns:
            StreamingResponse with converted data
            
        Raises:
            ExportError: If conversion fails
        """
        try:
            log.info(f"Starting format conversion to {target_format.value}")
            
            # Store connection manager for optimized processing
            self._connection_manager = connection_manager
            
            # For streaming conversions, we need to first save the parquet stream to a temp file
            temp_parquet_path = await self._save_parquet_stream(parquet_stream)
            
            # Apply intelligent format selection if enabled
            optimized_format = await self._apply_intelligent_format_selection(
                temp_parquet_path, target_format, options
            )
            
            if optimized_format != target_format:
                log.info(f"Intelligent format selection changed format from {target_format.value} to {optimized_format.value}")
                target_format = optimized_format
            
            if target_format == ExportFormat.CSV:
                return await self._convert_to_csv(temp_parquet_path, metadata, options)
            elif target_format == ExportFormat.EXCEL:
                if options.horizontal_facts:
                    return await self._convert_to_excel_facts(temp_parquet_path, metadata, options)
                else:
                    return await self._convert_to_excel(temp_parquet_path, metadata, options)
            elif target_format == ExportFormat.PARQUET:
                return await self._convert_to_parquet_direct(temp_parquet_path, metadata, options)
            else:
                raise ExportError(
                    error_type="unsupported_format",
                    message=f"Unsupported export format: {target_format.value}",
                    context={"format": target_format.value},
                    request_id=metadata.get("request_id", "unknown"),
                    recovery_suggestions=["Use one of: csv, excel, parquet"]
                )
                
        except Exception as e:
            log.error(f"Format conversion failed: {e}", exc_info=True)
            await self._cleanup_temp_files()
            
            if isinstance(e, ExportError):
                raise
            else:
                raise ExportError(
                    error_type="conversion_failed",
                    message=f"Format conversion to {target_format.value} failed: {str(e)}",
                    context={"format": target_format.value, "error": str(e)},
                    request_id=metadata.get("request_id", "unknown"),
                    recovery_suggestions=["Try a different export format", "Check data integrity"]
                )
    
    async def _save_parquet_stream(self, parquet_stream: AsyncGenerator[bytes, None]) -> str:
        """
        Save a parquet stream to a temporary file.
        
        Args:
            parquet_stream: Async generator yielding parquet data bytes
            
        Returns:
            Path to the temporary parquet file
        """
        if self.resource_manager:
            # Use ExportResourceManager for proper temporary file management
            return await self._save_parquet_stream_with_resource_manager(parquet_stream)
        else:
            # Legacy behavior using tempfile directly
            return await self._save_parquet_stream_legacy(parquet_stream)
    
    async def _save_parquet_stream_with_resource_manager(self, parquet_stream: AsyncGenerator[bytes, None]) -> str:
        """
        Save a parquet stream to a temporary file using ExportResourceManager with streaming approach.
        
        This method handles large files by streaming data directly to disk instead of
        loading everything into memory first.
        
        Args:
            parquet_stream: Async generator yielding parquet data bytes
            
        Returns:
            Path to the temporary parquet file
        """
        import tempfile
        import os
        from pathlib import Path
        
        try:
            # Create a temporary file directly for streaming large data
            temp_fd, temp_path = tempfile.mkstemp(
                suffix=".parquet",
                prefix="export_parquet_stream_",
                dir=self.resource_manager.temp_file_manager.base_directory
            )
            
            total_size = 0
            chunk_count = 0
            
            try:
                # Stream data directly to file to avoid memory issues
                with os.fdopen(temp_fd, 'wb') as temp_file:
                    async for chunk in parquet_stream:
                        if chunk:
                            temp_file.write(chunk)
                            total_size += len(chunk)
                            chunk_count += 1
                            
                            # Log progress for very large files
                            if chunk_count % 10000 == 0:
                                log.debug(f"Streamed {chunk_count} chunks, {total_size / (1024*1024):.1f} MB so far")
                
                log.info(f"Successfully streamed {chunk_count} chunks ({total_size / (1024*1024):.1f} MB) to parquet file")
                
                # Register the file with resource manager for cleanup
                # Use a custom approach since we created the file directly
                temp_path_obj = Path(temp_path)
                resource_id = self.resource_manager.register_temp_file(
                    file_path=temp_path_obj,
                    resource_id=None
                )
                
                log.debug(f"Registered large parquet file with resource manager: {temp_path_obj.name}")
                return str(temp_path_obj)
                
            except Exception as write_error:
                # Clean up the temporary file if writing failed
                try:
                    os.unlink(temp_path)
                except:
                    pass
                raise write_error
                
        except Exception as e:
            # Check if this is a file size validation error and try alternative approach
            error_str = str(e).lower()
            if "file size" in error_str and "exceeds maximum" in error_str:
                log.warning(f"File size limit exceeded, attempting direct streaming approach: {e}")
                return await self._save_large_parquet_stream_direct(parquet_stream)
            
            log.error(f"Failed to save parquet stream with resource manager: {e}")
            raise ExportError(
                error_type="stream_save_failed",
                message=f"Failed to save parquet stream: {str(e)}",
                context={
                    "error": str(e),
                    "total_size_mb": total_size / (1024*1024) if 'total_size' in locals() else 0,
                    "chunk_count": chunk_count if 'chunk_count' in locals() else 0
                },
                request_id="unknown",
                recovery_suggestions=[
                    "Check available disk space",
                    "Verify stream integrity", 
                    "Consider increasing TEMP_FILE_MAX_SIZE_MB for large exports",
                    "Check resource manager status"
                ]
            ) from e
    
    async def _save_large_parquet_stream_direct(self, parquet_stream: AsyncGenerator[bytes, None]) -> str:
        """
        Save a very large parquet stream directly to disk bypassing size limits.
        
        This method is used as a fallback for extremely large files that exceed
        the configured temporary file size limits.
        
        Args:
            parquet_stream: Async generator yielding parquet data bytes
            
        Returns:
            Path to the temporary parquet file
        """
        import tempfile
        import os
        from pathlib import Path
        
        try:
            # Create temporary file in the downloads directory
            downloads_dir = Path(self.resource_manager.temp_file_manager.base_directory)
            downloads_dir.mkdir(exist_ok=True)
            
            # Generate unique filename
            import uuid
            unique_id = str(uuid.uuid4())[:8]
            filename = f"large_export_parquet_{unique_id}.parquet"
            temp_path = downloads_dir / filename
            
            total_size = 0
            chunk_count = 0
            
            log.info(f"Starting direct streaming of large parquet file to: {filename}")
            
            # Stream data directly to file
            with open(temp_path, 'wb') as temp_file:
                async for chunk in parquet_stream:
                    if chunk:
                        temp_file.write(chunk)
                        total_size += len(chunk)
                        chunk_count += 1
                        
                        # Log progress every 10,000 chunks for large files
                        if chunk_count % 10000 == 0:
                            log.info(f"Large file progress: {chunk_count} chunks, {total_size / (1024*1024):.1f} MB")
            
            log.info(
                f"Successfully created large parquet file: {filename} "
                f"({chunk_count} chunks, {total_size / (1024*1024):.1f} MB)"
            )
            
            # Register with resource manager for cleanup (bypass size check)
            resource_id = self.resource_manager.register_temp_file(
                file_path=temp_path,
                resource_id=None
            )
            
            return str(temp_path)
            
        except Exception as e:
            log.error("Failed to save large parquet stream directly: {}", e)
            raise ExportError(
                error_type="large_file_save_failed",
                message=f"Failed to save large parquet stream: {str(e)}",
                context={
                    "error": str(e),
                    "total_size_mb": total_size / (1024*1024) if 'total_size' in locals() else 0,
                    "chunk_count": chunk_count if 'chunk_count' in locals() else 0
                },
                request_id="unknown",
                recovery_suggestions=[
                    "Check available disk space",
                    "Verify sufficient system resources for large file operations",
                    "Consider processing data in smaller batches",
                    "Contact system administrator if disk space is insufficient"
                ]
            ) from e

    async def _save_parquet_stream_legacy(self, parquet_stream: AsyncGenerator[bytes, None]) -> str:
        """
        Save a parquet stream to a temporary file using legacy tempfile approach.
        
        Args:
            parquet_stream: Async generator yielding parquet data bytes
            
        Returns:
            Path to the temporary parquet file
        """
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.parquet')
        temp_path = temp_file.name
        self.temp_files.append(temp_path)
        
        try:
            async for chunk in parquet_stream:
                temp_file.write(chunk)
            temp_file.close()
            
            log.debug(f"Saved parquet stream to temporary file (legacy): {temp_path}")
            return temp_path
            
        except Exception as e:
            temp_file.close()
            cleanup_parquet_file(temp_path)
            raise ExportError(
                error_type="stream_save_failed",
                message=f"Failed to save parquet stream: {str(e)}",
                context={"temp_path": temp_path, "error": str(e)},
                request_id="unknown",
                recovery_suggestions=["Check disk space", "Verify stream integrity"]
            ) from e
    
    async def _convert_to_csv(
        self, 
        parquet_path: str, 
        metadata: Dict[str, Any], 
        options: ConversionOptions
    ) -> StreamingResponse:
        """
        Convert parquet file to CSV streaming response.
        
        Args:
            parquet_path: Path to the parquet file
            metadata: Export metadata
            options: Conversion options
            
        Returns:
            StreamingResponse with CSV data
        """
        filename = metadata.get("filename", "export")
        
        async def csv_generator():
            """Generate CSV data from parquet file."""
            try:
                async for csv_chunk in convert_parquet_to_csv_stream(parquet_path):
                    yield csv_chunk.encode('utf-8')
            except Exception as e:
                log.error(f"CSV conversion error: {e}", exc_info=True)
                # Yield error message as CSV comment
                error_msg = f"# Error during CSV conversion: {str(e)}\n"
                yield error_msg.encode('utf-8')
        
        # Create cleanup task
        def cleanup():
            cleanup_parquet_file(parquet_path)
            
        background_task = BackgroundTask(cleanup)
        
        return StreamingResponse(
            content=csv_generator(),
            media_type="text/csv",
            headers={
                "Content-Disposition": f"attachment; filename={filename}.csv",
                "Cache-Control": "no-cache, no-store, must-revalidate"
            },
            background=background_task
        )
    
    async def _convert_to_excel(
        self, 
        parquet_path: str, 
        metadata: Dict[str, Any], 
        options: ConversionOptions
    ) -> StreamingResponse:
        """
        Convert parquet file to Excel format.
        
        Args:
            parquet_path: Path to the parquet file
            metadata: Export metadata
            options: Conversion options
            
        Returns:
            StreamingResponse with Excel data
        """
        filename = metadata.get("filename", "export")
        job_info = metadata.get("job_info")
        
        # Create Excel writer
        excel_writer = StreamingExcelWriter(filename, horizontal_facts=False)
        
        try:
            # Handle period separation if requested
            if options.separate_periods:
                # Check if we should use optimized approach for large datasets
                should_use_optimized = await self._should_use_optimized_period_processing(
                    parquet_path, metadata
                )
                
                if should_use_optimized:
                    log.info("Using optimized period processing for large dataset")
                    # Extract necessary information for optimized processing
                    table_name = metadata.get("table_name")
                    connection_manager = getattr(self, '_connection_manager', None)
                    request_id = metadata.get("request_id", "unknown")
                    
                    if table_name and connection_manager:
                        try:
                            await self._write_excel_with_periods_optimized(
                                excel_writer, table_name, metadata, connection_manager, request_id
                            )
                        except Exception as e:
                            log.warning(f"Optimized period processing failed, falling back to standard: {e}")
                            await self._write_excel_with_periods(excel_writer, parquet_path)
                    else:
                        log.warning("Missing table_name or connection_manager, using standard period processing")
                        await self._write_excel_with_periods(excel_writer, parquet_path)
                else:
                    await self._write_excel_with_periods(excel_writer, parquet_path)
            else:
                await self._write_excel_standard(excel_writer, parquet_path)
            
            # Add job info sheet if available
            if job_info:
                excel_writer.add_info_sheet(job_info)
            
            # Save Excel file
            excel_path = excel_writer.save()
            
            # Create streaming response
            async def excel_generator():
                with open(excel_path, 'rb') as f:
                    while True:
                        chunk = f.read(8192)
                        if not chunk:
                            break
                        yield chunk
            
            # Create cleanup task
            def cleanup():
                excel_writer.cleanup()
                cleanup_parquet_file(parquet_path)
                
            background_task = BackgroundTask(cleanup)
            
            return StreamingResponse(
                content=excel_generator(),
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={
                    "Content-Disposition": f"attachment; filename={filename}.xlsx",
                    "Cache-Control": "no-cache, no-store, must-revalidate"
                },
                background=background_task
            )
            
        except Exception as e:
            excel_writer.cleanup()
            raise ExportError(
                error_type="excel_conversion_failed",
                message=f"Excel conversion failed: {str(e)}",
                context={"parquet_path": parquet_path, "error": str(e)},
                request_id=metadata.get("request_id", "unknown"),
                recovery_suggestions=["Try CSV format instead", "Check data compatibility"]
            )
    
    async def _convert_to_excel_facts(
        self, 
        parquet_path: str, 
        metadata: Dict[str, Any], 
        options: ConversionOptions
    ) -> StreamingResponse:
        """
        Convert parquet file to Excel Facts format (horizontal layout).
        
        Args:
            parquet_path: Path to the parquet file
            metadata: Export metadata
            options: Conversion options
            
        Returns:
            StreamingResponse with Excel Facts data
        """
        filename = metadata.get("filename", "export")
        job_info = metadata.get("job_info")
        
        # Create Excel writer with horizontal facts enabled
        excel_writer = StreamingExcelWriter(filename, horizontal_facts=True)
        
        try:
            # Handle period separation if requested
            if options.separate_periods:
                # Check if we should use optimized approach for large datasets
                should_use_optimized = await self._should_use_optimized_period_processing(
                    parquet_path, metadata
                )
                
                if should_use_optimized:
                    log.info("Using optimized period processing for large facts dataset")
                    # Extract necessary information for optimized processing
                    table_name = metadata.get("table_name")
                    connection_manager = getattr(self, '_connection_manager', None)
                    request_id = metadata.get("request_id", "unknown")
                    
                    if table_name and connection_manager:
                        try:
                            await self._write_excel_facts_with_periods_optimized(
                                excel_writer, table_name, metadata, connection_manager, request_id
                            )
                        except Exception as e:
                            log.warning(f"Optimized facts period processing failed, falling back to standard: {e}")
                            await self._write_excel_facts_with_periods(excel_writer, parquet_path)
                    else:
                        log.warning("Missing table_name or connection_manager, using standard facts period processing")
                        await self._write_excel_facts_with_periods(excel_writer, parquet_path)
                else:
                    await self._write_excel_facts_with_periods(excel_writer, parquet_path)
            else:
                await self._write_excel_facts_standard(excel_writer, parquet_path)
            
            # Add job info sheet if available
            if job_info:
                excel_writer.add_info_sheet(job_info)
            
            # Save Excel file
            excel_path = excel_writer.save()
            
            # Create streaming response
            async def excel_generator():
                with open(excel_path, 'rb') as f:
                    while True:
                        chunk = f.read(8192)
                        if not chunk:
                            break
                        yield chunk
            
            # Create cleanup task
            def cleanup():
                excel_writer.cleanup()
                cleanup_parquet_file(parquet_path)
                
            background_task = BackgroundTask(cleanup)
            
            return StreamingResponse(
                content=excel_generator(),
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={
                    "Content-Disposition": f"attachment; filename={filename}.xlsx",
                    "Cache-Control": "no-cache, no-store, must-revalidate"
                },
                background=background_task
            )
            
        except Exception as e:
            excel_writer.cleanup()
            raise ExportError(
                error_type="excel_facts_conversion_failed",
                message=f"Excel Facts conversion failed: {str(e)}",
                context={"parquet_path": parquet_path, "error": str(e)},
                request_id=metadata.get("request_id", "unknown"),
                recovery_suggestions=["Try standard Excel format", "Check data structure"]
            )
    
    async def _convert_to_parquet_direct(
        self, 
        parquet_path: str, 
        metadata: Dict[str, Any], 
        options: ConversionOptions
    ) -> StreamingResponse:
        """
        Return parquet file directly (pass-through) with optional archiving.
        
        Args:
            parquet_path: Path to the parquet file
            metadata: Export metadata
            options: Conversion options
            
        Returns:
            StreamingResponse with parquet data (archived if requested)
        """
        filename = metadata.get("filename", "export")
        
        # Check if archiving is requested (default to True for parquet files)
        should_archive = metadata.get("archive_parquet", True)
        
        if should_archive:
            # Create archived version
            archived_path = await self._create_parquet_archive(parquet_path, filename)
            file_to_stream = archived_path
            content_type = "application/zip"
            file_extension = "zip"
            # Only track in temp_files if not using resource manager (legacy behavior)
            if not self.resource_manager:
                self.temp_files.append(archived_path)
        else:
            # Stream parquet file directly
            file_to_stream = parquet_path
            content_type = "application/octet-stream"
            file_extension = "parquet"
        
        async def file_generator():
            with open(file_to_stream, 'rb') as f:
                while True:
                    chunk = f.read(8192)
                    if not chunk:
                        break
                    yield chunk
        
        # Create cleanup task
        def cleanup():
            cleanup_parquet_file(parquet_path)
            if should_archive and archived_path != parquet_path:
                cleanup_parquet_file(archived_path)
            
        background_task = BackgroundTask(cleanup)
        
        return StreamingResponse(
            content=file_generator(),
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename={filename}.{file_extension}",
                "Cache-Control": "no-cache, no-store, must-revalidate"
            },
            background=background_task
        )
    
    async def _create_parquet_archive(self, parquet_path: str, filename: str) -> str:
        """
        Create a ZIP archive containing the parquet file.
        
        Args:
            parquet_path: Path to the parquet file to archive
            filename: Base filename for the archive
            
        Returns:
            Path to the created ZIP archive
        """
        if self.resource_manager:
            return await self._create_parquet_archive_with_resource_manager(parquet_path, filename)
        else:
            return await self._create_parquet_archive_legacy(parquet_path, filename)
    
    async def _create_parquet_archive_with_resource_manager(self, parquet_path: str, filename: str) -> str:
        """
        Create a ZIP archive containing the parquet file using ExportResourceManager.
        
        Args:
            parquet_path: Path to the parquet file to archive
            filename: Base filename for the archive
            
        Returns:
            Path to the created ZIP archive
        """
        import zipfile
        import io
        
        try:
            # Create ZIP archive in memory first
            zip_buffer = io.BytesIO()
            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED, allowZip64=True) as zipf:
                # Add parquet file to archive with clean filename
                arcname = f"{filename}.parquet"
                zipf.write(parquet_path, arcname)
            
            # Get the ZIP data
            zip_data = zip_buffer.getvalue()
            zip_buffer.close()
            
            # Create temporary file using resource manager
            resource_id = self.resource_manager.register_temp_file_from_manager(
                content=zip_data,
                extension="zip",
                prefix="export_archive_"
            )
            
            # Get the file path
            zip_path = self.resource_manager.get_temp_file_path(resource_id)
            if not zip_path:
                raise OSError(f"Failed to get file path for resource_id: {resource_id}")
            
            log.info(f"Created parquet archive via resource manager: {zip_path} (contains {filename}.parquet)")
            return str(zip_path)
            
        except Exception as e:
            log.error(f"Failed to create parquet archive with resource manager: {e}")
            raise ExportError(
                error_type="archive_creation_failed",
                message=f"Failed to create parquet archive: {str(e)}",
                context={"parquet_path": parquet_path, "filename": filename, "error": str(e)},
                request_id="unknown",
                recovery_suggestions=["Try direct parquet export without archiving", "Check disk space", "Check resource manager status"]
            ) from e
    
    async def _create_parquet_archive_legacy(self, parquet_path: str, filename: str) -> str:
        """
        Create a ZIP archive containing the parquet file using legacy tempfile approach.
        
        Args:
            parquet_path: Path to the parquet file to archive
            filename: Base filename for the archive
            
        Returns:
            Path to the created ZIP archive
        """
        import zipfile
        
        # Create temporary ZIP file
        zip_file = tempfile.NamedTemporaryFile(delete=False, suffix='.zip')
        zip_path = zip_file.name
        zip_file.close()
        
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED, allowZip64=True) as zipf:
                # Add parquet file to archive with clean filename
                arcname = f"{filename}.parquet"
                zipf.write(parquet_path, arcname)
                
            log.info(f"Created parquet archive (legacy): {zip_path} (contains {filename}.parquet)")
            return zip_path
            
        except Exception as e:
            # Clean up on error
            if os.path.exists(zip_path):
                os.unlink(zip_path)
            raise ExportError(
                error_type="archive_creation_failed",
                message=f"Failed to create parquet archive: {str(e)}",
                context={"parquet_path": parquet_path, "zip_path": zip_path, "error": str(e)},
                request_id="unknown",
                recovery_suggestions=["Try direct parquet export without archiving", "Check disk space"]
            ) from e
    
    async def _write_excel_standard(self, excel_writer: StreamingExcelWriter, parquet_path: str):
        """Write parquet data to Excel in standard format."""
        async for chunk in read_parquet_file_async_streaming(parquet_path):
            if chunk:
                excel_writer.write_data_chunk(chunk)
    
    async def _write_excel_with_periods(self, excel_writer: StreamingExcelWriter, parquet_path: str):
        """Write parquet data to Excel with period separation."""
        from magic_gateway.utils.parquet_processor import (
            group_parquet_data_by_periods_streaming,
            stream_period_temp_files_to_excel
        )
        from magic_gateway.utils.temp_file_manager import temp_file_manager
        
        try:
            # Group data by periods using streaming approach
            period_groups = await group_parquet_data_by_periods_streaming(parquet_path)
            
            # Check if we got file paths (memory-efficient approach) or data chunks (standard approach)
            if period_groups and isinstance(list(period_groups.values())[0], dict) and \
               list(period_groups.values())[0].get('type') == 'temp_file':
                # Memory-efficient approach - stream from temp files
                log.info("Using memory-efficient streaming from temporary files")
                await stream_period_temp_files_to_excel(period_groups, excel_writer, temp_file_manager)
            else:
                # Standard approach - data is already in memory as chunks
                log.info("Using standard in-memory period processing")
                for period_label, period_chunks in period_groups.items():
                    log.info(f"Processing period: {period_label} with {len(period_chunks)} chunks")
                    
                    # Write all chunks for this period (sheet will be created automatically)
                    for chunk in period_chunks:
                        if chunk:
                            excel_writer.write_period_data_chunk(chunk, period_label)
                        
        except Exception as e:
            log.warning(f"Period separation failed, falling back to standard format: {e}")
            # Fall back to standard format if period separation fails
            await self._write_excel_standard(excel_writer, parquet_path)
    
    async def _write_excel_with_periods_optimized(
        self, 
        excel_writer: StreamingExcelWriter, 
        table_name: str,
        metadata: Dict[str, Any],
        connection_manager,
        request_id: str
    ):
        """
        Write Excel with period separation using optimized approach for large datasets.
        
        This method downloads each period separately from ClickHouse to avoid loading
        all periods into memory at once.
        """
        from magic_gateway.utils.parquet_processor import (
            process_periods_separately_from_clickhouse,
            stream_period_temp_files_to_excel
        )
        from magic_gateway.utils.temp_file_manager import temp_file_manager
        
        try:
            log.info(f"Using optimized period processing for table {table_name}")
            
            # Process periods separately from ClickHouse
            period_temp_files = await process_periods_separately_from_clickhouse(
                table_name=table_name,
                metadata=metadata,
                connection_manager=connection_manager,
                temp_file_manager=temp_file_manager,
                request_id=request_id
            )
            
            # Stream each period to Excel separately
            await stream_period_temp_files_to_excel(
                period_file_mapping=period_temp_files,
                excel_writer=excel_writer,
                temp_file_manager=temp_file_manager
            )
            
            log.info(f"Successfully completed optimized period processing for {len(period_temp_files)} periods")
            
        except Exception as e:
            log.error(f"Optimized period processing failed: {e}")
            # Don't fall back to standard format here - let the caller handle the fallback
            raise
    
    async def _write_excel_facts_standard(self, excel_writer: StreamingExcelWriter, parquet_path: str):
        """Write parquet data to Excel Facts format."""
        async for chunk in read_parquet_file_async_streaming(parquet_path):
            if chunk:
                excel_writer.write_data_chunk(chunk)
    
    async def _write_excel_facts_with_periods(self, excel_writer: StreamingExcelWriter, parquet_path: str):
        """Write parquet data to Excel Facts format with period separation."""
        from magic_gateway.utils.parquet_processor import (
            group_parquet_data_by_periods_streaming,
            stream_period_temp_files_to_excel
        )
        from magic_gateway.utils.temp_file_manager import temp_file_manager
        
        try:
            # Group data by periods using streaming approach
            period_groups = await group_parquet_data_by_periods_streaming(parquet_path)
            
            # Check if we got file paths (memory-efficient approach) or data chunks (standard approach)
            if period_groups and isinstance(list(period_groups.values())[0], dict) and \
               list(period_groups.values())[0].get('type') == 'temp_file':
                # Memory-efficient approach - stream from temp files
                log.info("Using memory-efficient facts streaming from temporary files")
                await stream_period_temp_files_to_excel(period_groups, excel_writer, temp_file_manager)
            else:
                # Standard approach - data is already in memory as chunks
                log.info("Using standard in-memory facts period processing")
                for period_label, period_chunks in period_groups.items():
                    log.info(f"Processing period (facts): {period_label} with {len(period_chunks)} chunks")
                    
                    # Write all chunks for this period in facts format (sheet will be created automatically)
                    for chunk in period_chunks:
                        if chunk:
                            excel_writer.write_period_data_chunk(chunk, period_label)
                        
        except Exception as e:
            log.warning(f"Period separation failed for facts format, falling back to standard facts: {e}")
            # Fall back to standard facts format if period separation fails
            await self._write_excel_facts_standard(excel_writer, parquet_path)
    
    async def _write_excel_facts_with_periods_optimized(
        self, 
        excel_writer: StreamingExcelWriter, 
        table_name: str,
        metadata: Dict[str, Any],
        connection_manager,
        request_id: str
    ):
        """
        Write Excel Facts with period separation using optimized approach for large datasets.
        
        This method downloads each period separately from ClickHouse to avoid loading
        all periods into memory at once.
        """
        from magic_gateway.utils.parquet_processor import (
            process_periods_separately_from_clickhouse,
            stream_period_temp_files_to_excel
        )
        from magic_gateway.utils.temp_file_manager import temp_file_manager
        
        try:
            log.info(f"Using optimized period processing for facts format, table {table_name}")
            
            # Process periods separately from ClickHouse
            period_temp_files = await process_periods_separately_from_clickhouse(
                table_name=table_name,
                metadata=metadata,
                connection_manager=connection_manager,
                temp_file_manager=temp_file_manager,
                request_id=request_id
            )
            
            # Stream each period to Excel separately
            await stream_period_temp_files_to_excel(
                period_file_mapping=period_temp_files,
                excel_writer=excel_writer,
                temp_file_manager=temp_file_manager
            )
            
            log.info(f"Successfully completed optimized facts period processing for {len(period_temp_files)} periods")
            
        except Exception as e:
            log.error(f"Optimized facts period processing failed: {e}")
            # Don't fall back to standard format here - let the caller handle the fallback
            raise
    
    async def _should_use_optimized_period_processing(
        self, 
        parquet_path: str, 
        metadata: Dict[str, Any]
    ) -> bool:
        """
        Determine if optimized period processing should be used based on dataset size and metadata.
        
        Args:
            parquet_path: Path to the parquet file
            metadata: Export metadata
            
        Returns:
            True if optimized processing should be used, False otherwise
        """
        try:
            from magic_gateway.utils.parquet_processor import get_parquet_file_info
            from magic_gateway.core.config import settings
            
            # Check if periods are available in metadata
            job_info = metadata.get("job_info", {})
            periods = job_info.get("periods", [])
            
            if not periods or len(periods) <= 1:
                # No benefit from optimized processing with single or no periods
                return False
            
            # Get file information
            file_info = get_parquet_file_info(parquet_path)
            if "error" in file_info:
                log.warning(f"Could not analyze parquet file for optimization decision: {file_info['error']}")
                return False
            
            num_rows = file_info.get("num_rows", 0)
            file_size_mb = file_info.get("file_size_bytes", 0) / (1024 * 1024)
            
            # Use optimized processing for large datasets with multiple periods
            large_dataset_threshold = getattr(settings, 'LARGE_DATASET_THRESHOLD_ROWS', 50_000_000)
            large_file_threshold_mb = 500  # 500MB threshold
            
            should_optimize = (
                num_rows > large_dataset_threshold or 
                file_size_mb > large_file_threshold_mb
            ) and len(periods) > 1
            
            if should_optimize:
                log.info(
                    f"Optimized period processing recommended: {num_rows:,} rows, "
                    f"{file_size_mb:.1f} MB, {len(periods)} periods"
                )
            else:
                log.debug(
                    f"Standard period processing: {num_rows:,} rows, "
                    f"{file_size_mb:.1f} MB, {len(periods)} periods"
                )
            
            return should_optimize
            
        except Exception as e:
            log.warning(f"Error determining optimization strategy, using standard processing: {e}")
            return False

    async def _cleanup_temp_files(self):
        """Clean up all temporary files created during conversion."""
        for temp_file in self.temp_files:
            cleanup_parquet_file(temp_file)
        self.temp_files.clear()
    
    async def __aenter__(self):
        """Async context manager entry."""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit with cleanup."""
        await self._cleanup_temp_files()
    
    async def _apply_intelligent_format_selection(
        self, 
        parquet_path: str, 
        target_format: ExportFormat, 
        options: ConversionOptions
    ) -> ExportFormat:
        """
        Apply intelligent format selection based on data characteristics.
        
        Args:
            parquet_path: Path to the parquet file
            target_format: Originally requested format
            options: Conversion options
            
        Returns:
            Optimized format based on data analysis
        """
        # Note: intelligent_format functionality will be removed in cleanup phase
        # For now, always return the target format
        if True:  # Placeholder for intelligent format logic removal
            return target_format
            
        try:
            from magic_gateway.utils.parquet_processor import get_parquet_file_info
            
            # Get file information for analysis
            file_info = get_parquet_file_info(parquet_path)
            
            if "error" in file_info:
                log.warning(f"Could not analyze parquet file for intelligent format selection: {file_info['error']}")
                return target_format
            
            num_rows = file_info.get("num_rows", 0)
            num_columns = file_info.get("num_columns", 0)
            file_size_mb = file_info.get("file_size_bytes", 0) / (1024 * 1024)
            
            log.info(f"Intelligent format analysis: {num_rows} rows, {num_columns} columns, {file_size_mb:.1f} MB")
            
            # Apply intelligent format selection rules
            if target_format == ExportFormat.EXCEL:
                # Excel has row limits - switch to CSV for very large datasets
                if num_rows > 900000:  # Excel practical limit
                    log.info(f"Dataset too large for Excel ({num_rows} rows), switching to CSV")
                    return ExportFormat.CSV
                
                # For very wide datasets, prefer CSV
                if num_columns > 100:
                    log.info(f"Dataset too wide for Excel ({num_columns} columns), switching to CSV")
                    return ExportFormat.CSV
                
                # For large files, prefer CSV for performance
                if file_size_mb > 100:
                    log.info(f"File too large for Excel ({file_size_mb:.1f} MB), switching to CSV")
                    return ExportFormat.CSV
            
            return target_format
            
        except Exception as e:
            log.warning(f"Error in intelligent format selection: {e}")
            return target_format