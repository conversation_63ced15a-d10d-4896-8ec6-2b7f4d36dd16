"""
Metadata analysis service for export optimization.

This module provides the MetadataAnalyzer service that queries job metadata
to determine optimal export strategies, calculate size estimates, and validate
export constraints.
"""

import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional

from magic_gateway.core.logging_config import log
from magic_gateway.db.connection_manager import ClickHouseConnectionManager
from magic_gateway.export.exceptions import (
    ExcelLimitExceededError,
    ExportError,
    ExportErrorContext,
    MetadataRetrievalError,
)
from magic_gateway.export.models import (
    ExportFormat,
    ExportOptions,
    JobMetadata,
    OptimizationStrategy,
    PeriodSeparation,
    SizeEstimate,
    TempFileStrategy,
)


class MetadataAnalyzer:
    """
    Service for analyzing job metadata to optimize export operations.

    This class provides methods to retrieve job metadata, calculate export size
    estimates, validate constraints, and determine optimal chunking strategies
    for different export formats and dataset sizes.
    """

    def __init__(self, connection_manager: ClickHouseConnectionManager):
        """
        Initialize the metadata analyzer with a ClickHouse connection manager.

        Args:
            connection_manager: ClickHouse connection manager for database queries
        """
        self.connection_manager = connection_manager

    async def analyze_job_metadata(
        self, job_id: int, request_id: Optional[str] = None
    ) -> JobMetadata:
        """
        Analyze and retrieve comprehensive metadata for a job.

        This method queries the metadata table to extract all information needed
        for export optimization, including row counts, available periods, facts
        list, and axes information.

        Args:
            job_id: The job ID to analyze
            request_id: Optional request ID for error tracking

        Returns:
            JobMetadata object containing comprehensive job information

        Raises:
            MetadataRetrievalError: If metadata cannot be retrieved or is invalid
            ExportError: For other database or connection errors
        """
        request_id = request_id or str(uuid.uuid4())

        with ExportErrorContext(request_id, "metadata_analysis", job_id) as ctx:
            ctx.add_context("job_id", job_id)

            try:
                log.debug(
                    f"Starting metadata analysis for job {job_id} (request {request_id})"
                )

                # Validate connection manager is initialized
                if not self.connection_manager.initialized:
                    raise ExportError(
                        error_type="connection_not_initialized",
                        message="ClickHouse connection manager is not initialized",
                        context={"job_id": job_id},
                        request_id=request_id,
                        recovery_suggestions=[
                            "Wait for connection initialization to complete",
                            "Check connection manager startup logs",
                        ],
                    )

                async with self.connection_manager.connection() as conn:
                    # Query to get comprehensive job metadata from the actual metadata table
                    # Use string formatting to avoid parameter conflicts with ClickHouse functions
                    metadata_query = f"""
                    SELECT
                        job_id,
                        final_result_table as table_name,
                        CASE
                            WHEN position(final_result_table, '.') > 0
                            THEN substring(final_result_table, 1, position(final_result_table, '.') - 1)
                            ELSE 'kpi_results'
                        END as database_name,
                        result_rows as total_rows,
                        periods as available_periods,
                        facts as facts_list,
                        job_info as job_info,
                        (result_rows * 100.0 / 1024 / 1024) as estimated_size_mb,
                        created_at
                    FROM metadata.results_metadata
                    WHERE job_id = '{str(job_id)}'
                    AND status = 'done'
                    AND lifecycle_status = 'ACTIVE'
                    ORDER BY created_at DESC
                    LIMIT 1
                    """

                    log.debug(f"Executing metadata query for job {job_id}")
                    result = conn.execute(metadata_query)

                    if not result or len(result) == 0:
                        raise MetadataRetrievalError(
                            request_id=request_id,
                            job_id=job_id,
                            error_details="No metadata found for the specified job ID",
                            context={"job_id": job_id, "query": metadata_query},
                        )

                    row = result[0]

                    # Parse the metadata fields
                    try:
                        # Handle job_info first to extract periods and other information
                        job_info = self._parse_job_info_field(row[6])

                        # Extract periods from job_info if available, otherwise fall back to periods column
                        if job_info and "periods" in job_info:
                            available_periods = (
                                job_info["periods"]
                                if isinstance(job_info["periods"], list)
                                else []
                            )
                            log.debug(
                                f"Extracted {len(available_periods)} periods from job_info for job {job_id}"
                            )
                        else:
                            # Fall back to the periods column
                            available_periods = self._parse_periods_field(row[4])
                            log.debug(
                                f"Extracted {len(available_periods)} periods from periods column for job {job_id}"
                            )

                        # Extract facts from job_info if available, otherwise fall back to facts column
                        if job_info and "facts" in job_info:
                            facts_list = (
                                job_info["facts"]
                                if isinstance(job_info["facts"], list)
                                else []
                            )
                            log.debug(
                                f"Extracted {len(facts_list)} facts from job_info for job {job_id}"
                            )
                        else:
                            # Fall back to the facts column
                            facts_list = self._parse_facts_field(row[5])
                            log.debug(
                                f"Extracted {len(facts_list)} facts from facts column for job {job_id}"
                            )

                        # Validate job_info structure and extract axes information
                        if not job_info:
                            raise MetadataRetrievalError(
                                request_id=request_id,
                                job_id=job_id,
                                error_details="job_info field is missing or empty",
                                context={
                                    "table_name": row[1] if len(row) > 1 else "unknown"
                                },
                            )

                        # Create JobMetadata object
                        metadata = JobMetadata(
                            job_id=row[0],
                            table_name=row[1],
                            database_name=row[2],
                            total_rows=row[3],
                            available_periods=available_periods,
                            facts_list=facts_list,
                            job_info=job_info,  # Changed from axes_info to job_info
                            estimated_size_mb=(
                                float(row[7]) if row[7] is not None else 0.0
                            ),
                            created_at=(
                                row[8]
                                if isinstance(row[8], datetime)
                                else datetime.now()
                            ),
                        )

                        log.info(
                            f"Successfully retrieved metadata for job {job_id}: "
                            f"{metadata.total_rows:,} rows, {len(metadata.available_periods)} periods, "
                            f"{len(metadata.facts_list)} facts"
                        )

                        ctx.add_context(
                            "metadata",
                            {
                                "total_rows": metadata.total_rows,
                                "periods_count": len(metadata.available_periods),
                                "facts_count": len(metadata.facts_list),
                                "estimated_size_mb": metadata.estimated_size_mb,
                            },
                        )

                        return metadata

                    except Exception as parse_error:
                        raise MetadataRetrievalError(
                            request_id=request_id,
                            job_id=job_id,
                            error_details=f"Failed to parse metadata fields: {str(parse_error)}",
                            context={
                                "job_id": job_id,
                                "parse_error": str(parse_error),
                                "raw_row": str(row),
                            },
                        )

            except MetadataRetrievalError:
                # Re-raise metadata errors as-is
                raise
            except ExportError:
                # Re-raise export errors as-is
                raise
            except Exception as e:
                error_msg = str(e).lower()

                # Classify database errors
                if "table" in error_msg and (
                    "not found" in error_msg or "doesn't exist" in error_msg
                ):
                    raise MetadataRetrievalError(
                        request_id=request_id,
                        job_id=job_id,
                        error_details="Metadata table not found or inaccessible",
                        context={"job_id": job_id, "database_error": str(e)},
                    )
                elif "timeout" in error_msg or "connection" in error_msg:
                    raise ExportError(
                        error_type="database_connection_error",
                        message=f"Database connection error during metadata retrieval: {str(e)}",
                        context={"job_id": job_id, "error": str(e)},
                        request_id=request_id,
                        recovery_suggestions=[
                            "Retry the request",
                            "Check ClickHouse server connectivity",
                            "Verify database configuration",
                        ],
                    )
                else:
                    raise MetadataRetrievalError(
                        request_id=request_id,
                        job_id=job_id,
                        error_details=f"Unexpected database error: {str(e)}",
                        context={
                            "job_id": job_id,
                            "error": str(e),
                            "error_type": type(e).__name__,
                        },
                    )

    def _parse_periods_field(self, periods_data: Any) -> List[str]:
        """
        Parse the available_periods field from the database.

        Args:
            periods_data: Raw periods data from database (JSON array or string)

        Returns:
            List of period strings
        """
        if not periods_data:
            return []

        if isinstance(periods_data, list):
            return [str(p) for p in periods_data]
        elif isinstance(periods_data, str):
            # Try to parse as JSON first
            try:
                import json

                parsed = json.loads(periods_data)
                if isinstance(parsed, list):
                    return [str(p) for p in parsed]
            except (json.JSONDecodeError, ValueError):
                pass

            # Fall back to comma-separated parsing
            return [p.strip() for p in periods_data.split(",") if p.strip()]
        else:
            return [str(periods_data)]

    def _parse_facts_field(self, facts_data: Any) -> List[str]:
        """
        Parse the facts_list field from the database.

        Args:
            facts_data: Raw facts data from database (JSON array or string)

        Returns:
            List of fact names
        """
        if not facts_data:
            return []

        if isinstance(facts_data, list):
            return [str(f) for f in facts_data]
        elif isinstance(facts_data, str):
            # Try to parse as JSON first
            try:
                import json

                parsed = json.loads(facts_data)
                if isinstance(parsed, list):
                    return [str(f) for f in parsed]
            except (json.JSONDecodeError, ValueError):
                pass

            # Fall back to comma-separated parsing
            return [f.strip() for f in facts_data.split(",") if f.strip()]
        else:
            return [str(facts_data)]

    def _parse_job_info_field(self, job_info_data: Any) -> Dict[str, Any]:
        """
        Parse the job_info field from the database.

        Args:
            job_info_data: Raw job_info data from database (JSON object or string)

        Returns:
            Dictionary containing job information including axes, periods, facts, etc.

        Raises:
            ValueError: If job_info is malformed or missing required structure
        """
        if not job_info_data:
            return {}

        if isinstance(job_info_data, dict):
            return job_info_data
        elif isinstance(job_info_data, str):
            try:
                import json

                parsed = json.loads(job_info_data)
                if isinstance(parsed, dict):
                    return parsed
                else:
                    raise ValueError(
                        f"job_info must be a JSON object, got {type(parsed)}"
                    )
            except (json.JSONDecodeError, ValueError) as e:
                raise ValueError(f"Invalid job_info JSON format: {str(e)}")
        else:
            raise ValueError(
                f"job_info must be a string or dict, got {type(job_info_data)}"
            )

    def _parse_axes_field(self, axes_data: Any) -> Dict[str, Any]:
        """
        Legacy method for backward compatibility.
        Now delegates to _parse_job_info_field.
        """
        return self._parse_job_info_field(axes_data)

    def _extract_axes_from_job_info(self, job_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract axes information from job_info structure.

        Args:
            job_info: Parsed job_info dictionary

        Returns:
            Dictionary containing axes information

        Raises:
            ValueError: If axes information is missing or malformed
        """
        if not isinstance(job_info, dict):
            raise ValueError("job_info must be a dictionary")

        # Extract axes from the nested "axes" object within job_info
        axes = job_info.get("axes", {})

        if not axes:
            # Log warning but don't fail - some jobs might not have axes
            log.warning("No axes information found in job_info")
            return {}

        if not isinstance(axes, dict):
            raise ValueError(f"axes field must be a dictionary, got {type(axes)}")

        return axes

    async def calculate_export_size_estimates(
        self,
        metadata: JobMetadata,
        format: ExportFormat,
        request_id: Optional[str] = None,
    ) -> SizeEstimate:
        """
        Calculate export size estimates for different formats and layouts.

        This method estimates the file sizes for various export formats based on
        the job metadata, helping to determine optimal export strategies and
        validate resource constraints.

        Args:
            metadata: Job metadata containing row counts and structure information
            format: Target export format for size estimation
            request_id: Optional request ID for error tracking

        Returns:
            SizeEstimate object with size estimates for different formats

        Raises:
            ExportError: If size calculation fails
        """
        request_id = request_id or str(uuid.uuid4())

        try:
            log.debug(
                f"Calculating size estimates for job {metadata.job_id} in {format} format"
            )

            # Base calculations using metadata
            total_rows = metadata.total_rows
            facts_count = len(metadata.facts_list)
            periods_count = len(metadata.available_periods)

            # Calculate rows per period for period-based chunking
            rows_per_period = {}
            if periods_count > 0:
                avg_rows_per_period = total_rows // periods_count
                for period in metadata.available_periods:
                    # Use average for now - could be enhanced with actual period row counts
                    rows_per_period[period] = avg_rows_per_period

            # Estimate average row size in bytes based on facts and typical data types
            # This is a heuristic - could be enhanced with actual column type analysis
            estimated_bytes_per_row = self._estimate_bytes_per_row(metadata)

            # Calculate format-specific size estimates
            csv_size_mb = self._calculate_csv_size(total_rows, estimated_bytes_per_row)
            excel_vertical_size_mb = self._calculate_excel_vertical_size(
                total_rows, estimated_bytes_per_row
            )
            excel_horizontal_size_mb = self._calculate_excel_horizontal_size(
                total_rows, facts_count, estimated_bytes_per_row
            )
            parquet_size_mb = self._calculate_parquet_size(
                total_rows, estimated_bytes_per_row
            )

            size_estimate = SizeEstimate(
                csv_size_mb=csv_size_mb,
                excel_vertical_size_mb=excel_vertical_size_mb,
                excel_horizontal_size_mb=excel_horizontal_size_mb,
                parquet_size_mb=parquet_size_mb,
                rows_per_period=rows_per_period,
            )

            log.info(
                f"Size estimates for job {metadata.job_id}: "
                f"CSV: {csv_size_mb:.1f}MB, Excel Vertical: {excel_vertical_size_mb:.1f}MB, "
                f"Excel Horizontal: {excel_horizontal_size_mb:.1f}MB, Parquet: {parquet_size_mb:.1f}MB"
            )

            return size_estimate

        except Exception as e:
            raise ExportError(
                error_type="size_calculation_error",
                message=f"Failed to calculate export size estimates: {str(e)}",
                context={
                    "job_id": metadata.job_id,
                    "format": format,
                    "total_rows": metadata.total_rows,
                    "facts_count": len(metadata.facts_list),
                    "error": str(e),
                },
                request_id=request_id,
                recovery_suggestions=[
                    "Retry the size calculation",
                    "Check job metadata integrity",
                    "Contact support if the issue persists",
                ],
            )

    def _estimate_bytes_per_row(self, metadata: JobMetadata) -> int:
        """
        Estimate average bytes per row based on metadata.

        This is a heuristic calculation that could be enhanced with actual
        column type analysis from the database schema.

        Args:
            metadata: Job metadata

        Returns:
            Estimated bytes per row
        """
        # Base overhead for row structure
        base_overhead = 50

        # Estimate based on number of facts and typical data types
        facts_count = len(metadata.facts_list)

        # Assume mix of data types:
        # - Numeric facts: ~8 bytes each
        # - String dimensions: ~20 bytes each on average
        # - Dates/timestamps: ~20 bytes each

        # For vertical layout: dimensions + facts
        estimated_columns = facts_count + 5  # Assume ~5 dimension columns on average
        avg_bytes_per_column = 15  # Mixed data types average

        estimated_bytes = base_overhead + (estimated_columns * avg_bytes_per_column)

        # Add some buffer for formatting and encoding
        return int(estimated_bytes * 1.2)

    def _calculate_csv_size(self, total_rows: int, bytes_per_row: int) -> float:
        """Calculate estimated CSV file size in MB."""
        # CSV has minimal overhead, mostly just data + delimiters
        csv_overhead_factor = 1.1  # 10% overhead for delimiters and headers
        total_bytes = total_rows * bytes_per_row * csv_overhead_factor
        return total_bytes / (1024 * 1024)  # Convert to MB

    def _calculate_excel_vertical_size(
        self, total_rows: int, bytes_per_row: int
    ) -> float:
        """Calculate estimated Excel file size for vertical layout in MB."""
        # Excel has significant overhead due to XML structure and formatting
        excel_overhead_factor = (
            2.5  # Excel files are typically 2-3x larger than raw data
        )
        total_bytes = total_rows * bytes_per_row * excel_overhead_factor
        return total_bytes / (1024 * 1024)  # Convert to MB

    def _calculate_excel_horizontal_size(
        self, total_rows: int, facts_count: int, bytes_per_row: int
    ) -> float:
        """Calculate estimated Excel file size for horizontal layout in MB."""
        # Horizontal layout typically has fewer rows but more columns
        # Estimate row reduction based on facts being pivoted to columns
        if facts_count > 1:
            # Rough estimate: rows reduced by facts count, but columns increased
            estimated_horizontal_rows = total_rows // facts_count
            # Adjust bytes per row for wider rows
            horizontal_bytes_per_row = bytes_per_row * 1.5
        else:
            estimated_horizontal_rows = total_rows
            horizontal_bytes_per_row = bytes_per_row

        excel_overhead_factor = 2.5
        total_bytes = (
            estimated_horizontal_rows * horizontal_bytes_per_row * excel_overhead_factor
        )
        return total_bytes / (1024 * 1024)  # Convert to MB

    def _calculate_parquet_size(self, total_rows: int, bytes_per_row: int) -> float:
        """Calculate estimated Parquet file size in MB."""
        # Parquet is highly compressed columnar format
        parquet_compression_factor = 0.3  # Parquet typically achieves 70% compression
        total_bytes = total_rows * bytes_per_row * parquet_compression_factor
        return total_bytes / (1024 * 1024)  # Convert to MB

    async def validate_excel_limits(
        self,
        metadata: JobMetadata,
        options: ExportOptions,
        request_id: Optional[str] = None,
    ) -> bool:
        """
        Validate that Excel export will not exceed row limits.

        Excel has a hard limit of 1,048,576 rows per sheet. This method validates
        that the export will not exceed this limit, considering layout options
        and period separation.

        Args:
            metadata: Job metadata containing row count information
            options: Export options including layout and period separation
            request_id: Optional request ID for error tracking

        Returns:
            True if export is within Excel limits

        Raises:
            ExcelLimitExceededError: If Excel row limits would be exceeded
            ExportError: For other validation errors
        """
        request_id = request_id or str(uuid.uuid4())

        try:
            EXCEL_ROW_LIMIT = 1048576

            log.debug(f"Validating Excel limits for job {metadata.job_id}")

            # Calculate effective row count based on layout and options
            if options.horizontal_facts and len(metadata.facts_list) > 1:
                # Horizontal layout reduces rows by pivoting facts to columns
                effective_rows = metadata.total_rows // len(metadata.facts_list)
                log.debug(
                    f"Horizontal layout: {metadata.total_rows} rows reduced to ~{effective_rows} rows"
                )
            else:
                effective_rows = metadata.total_rows

            # Check if period separation is enabled
            if (
                options.separate_periods_mode == PeriodSeparation.SHEETS
                or options.separate_periods_mode == PeriodSeparation.FILES
            ) and len(metadata.available_periods) > 1:
                # With period separation, each period goes to a separate sheet
                max_rows_per_sheet = max(
                    effective_rows // len(metadata.available_periods),
                    effective_rows // len(metadata.available_periods)
                    + (effective_rows % len(metadata.available_periods)),
                )
                log.debug(f"Period separation: max {max_rows_per_sheet} rows per sheet")
            else:
                # All data in one sheet
                max_rows_per_sheet = effective_rows

            # Validate against Excel limit
            if max_rows_per_sheet > EXCEL_ROW_LIMIT:
                raise ExcelLimitExceededError(
                    request_id=request_id,
                    total_rows=max_rows_per_sheet,
                    excel_limit=EXCEL_ROW_LIMIT,
                    context={
                        "job_id": metadata.job_id,
                        "original_rows": metadata.total_rows,
                        "effective_rows": effective_rows,
                        "max_rows_per_sheet": max_rows_per_sheet,
                        "horizontal_facts": options.horizontal_facts,
                        "separate_periods": options.separate_periods,
                        "periods_count": len(metadata.available_periods),
                        "facts_count": len(metadata.facts_list),
                    },
                )

            log.info(
                f"Excel limits validation passed for job {metadata.job_id}: {max_rows_per_sheet:,} rows per sheet"
            )
            return True

        except ExcelLimitExceededError:
            # Re-raise Excel limit errors as-is
            raise
        except Exception as e:
            raise ExportError(
                error_type="excel_validation_error",
                message=f"Failed to validate Excel limits: {str(e)}",
                context={
                    "job_id": metadata.job_id,
                    "total_rows": metadata.total_rows,
                    "error": str(e),
                },
                request_id=request_id,
                recovery_suggestions=[
                    "Retry the validation",
                    "Check export options configuration",
                    "Contact support if the issue persists",
                ],
            )

    async def determine_chunking_strategy(
        self, metadata: JobMetadata, request_id: Optional[str] = None
    ) -> OptimizationStrategy:
        """
        Determine the optimal chunking strategy based on dataset characteristics.

        This method analyzes the dataset size and structure to determine the best
        approach for processing the export, including memory limits, chunk sizes,
        and temporary file strategies.

        Args:
            metadata: Job metadata containing dataset characteristics
            request_id: Optional request ID for error tracking

        Returns:
            OptimizationStrategy with recommended processing approach

        Raises:
            ExportError: If strategy determination fails
        """
        request_id = request_id or str(uuid.uuid4())

        try:
            log.debug(f"Determining chunking strategy for job {metadata.job_id}")

            total_rows = metadata.total_rows
            periods_count = len(metadata.available_periods)

            # Define thresholds for different strategies
            LARGE_DATASET_THRESHOLD = 1_000_000  # 1M rows
            VERY_LARGE_DATASET_THRESHOLD = 10_000_000  # 10M rows

            # Determine if streaming is needed
            use_streaming = (
                total_rows > 100_000
            )  # Use streaming for datasets > 100K rows

            # Determine chunk size based on dataset size
            if total_rows <= 100_000:
                chunk_size = total_rows  # Use one chunk for small datasets
                memory_limit_mb = 256
            elif total_rows <= LARGE_DATASET_THRESHOLD:
                chunk_size = 50_000
                memory_limit_mb = 512
            elif total_rows <= VERY_LARGE_DATASET_THRESHOLD:
                chunk_size = 100_000
                memory_limit_mb = 1024
            else:
                chunk_size = 1_000_000  # 1M rows for very large datasets
                memory_limit_mb = 10240  # 10GB memory limit

            # Determine if period-based chunking is beneficial
            period_chunking = (
                total_rows >= LARGE_DATASET_THRESHOLD
                and periods_count > 1
                and periods_count <= 50  # Don't use period chunking if too many periods
            )

            # Determine temporary file strategy
            if total_rows < 100_000:
                temp_file_strategy = TempFileStrategy.MEMORY_ONLY
            elif period_chunking:
                temp_file_strategy = TempFileStrategy.PERIOD_CHUNKS
            else:
                temp_file_strategy = TempFileStrategy.SINGLE_TEMP

            strategy = OptimizationStrategy(
                use_streaming=use_streaming,
                chunk_size=chunk_size,
                period_chunking=period_chunking,
                memory_limit_mb=memory_limit_mb,
                temp_file_strategy=temp_file_strategy,
            )

            log.info(
                f"Chunking strategy for job {metadata.job_id}: "
                f"streaming={use_streaming}, chunk_size={chunk_size:,}, "
                f"period_chunking={period_chunking}, memory_limit={memory_limit_mb}MB, "
                f"temp_strategy={temp_file_strategy}"
            )

            return strategy

        except Exception as e:
            raise ExportError(
                error_type="strategy_determination_error",
                message=f"Failed to determine chunking strategy: {str(e)}",
                context={
                    "job_id": metadata.job_id,
                    "total_rows": metadata.total_rows,
                    "periods_count": len(metadata.available_periods),
                    "error": str(e),
                },
                request_id=request_id,
                recovery_suggestions=[
                    "Retry the strategy determination",
                    "Check job metadata integrity",
                    "Use default chunking strategy if the issue persists",
                ],
            )
