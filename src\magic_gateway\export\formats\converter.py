"""
Base format converter for streaming export operations.

This module provides the core FormatConverter class that handles streaming
conversion of data from ClickHouse to various export formats with temporary
file management integration.
"""

import asyncio
import csv
import io
import uuid
import zipfile
from pathlib import Path
from typing import Any, AsyncGenerator, Dict, List, Optional

from magic_gateway.core.logging_config import log
from magic_gateway.core.config import settings
from magic_gateway.db.connection_manager import ClickHouseConnectionManager
from magic_gateway.utils.parquet_processor import (
    get_memory_usage_mb,
    monitor_memory_and_adjust_chunk_size,
    MEMORY_THRESHOLD_MB,
    MIN_CHUNK_SIZE,
    MAX_CHUNK_SIZE,
)
from magic_gateway.export.exceptions import (
    ExcelLimitExceededError,
    ExportError,
    ExportErrorContext,
    FormatConversionError,
    StreamingDataError,
)
from magic_gateway.export.models import (
    ExportFormat,
    ExportOptions,
    JobMetadata,
    ExcelLayout,
    PeriodSeparation,
)
from magic_gateway.utils.temp_file_manager import temp_file_manager
from magic_gateway.export.formats.excel_streamer import ExcelStreamingWriter
from magic_gateway.utils.info_sheet_generator import InfoSheetGenerator
from magic_gateway.export.utils.query_utils import (
    add_period_filter_to_query,
    get_standardized_column_names,
)
import zipfile
from typing import Any


class FormatConverter:
    """
    Base format converter for streaming export operations.

    This class provides streaming conversion of data directly from SQLAlchemy
    results to various export formats without intermediate DataFrame conversion.
    It integrates with the temporary file management system for efficient
    resource handling.
    """

    def __init__(self, connection_manager: ClickHouseConnectionManager):
        """
        Initialize the format converter.

        Args:
            connection_manager: ClickHouse connection manager instance
        """
        self.connection_manager = connection_manager
        self.cluster_name = getattr(connection_manager, "cluster_name", "primary")

    async def convert_to_csv_streaming(
        self,
        query: str,
        options: ExportOptions,
        temp_file_path: str,
        request_id: Optional[str] = None,
        job_metadata: Optional[JobMetadata] = None,
    ) -> str:
        """
        Convert streaming data to CSV format with ZIP compression.

        This method streams data directly from ClickHouse and writes it to CSV
        format with optional ZIP compression. Supports period separation by
        creating separate files for each period.

        Args:
            query: SQL query to execute for data retrieval
            options: Export options including compression and period separation
            temp_file_path: Path for temporary file storage
            request_id: Optional request ID for error tracking
            job_metadata: Optional job metadata for period information

        Returns:
            Path to the generated CSV file (or ZIP file if compressed)

        Raises:
            FormatConversionError: If CSV conversion fails
            StreamingDataError: If data streaming fails
        """
        request_id = request_id or str(uuid.uuid4())

        with ExportErrorContext(request_id, "csv_conversion") as ctx:
            ctx.add_context("format", ExportFormat.CSV.value)
            ctx.add_context("compression", options.compression)
            ctx.add_context("separate_periods", options.separate_periods)

            try:
                if not self.connection_manager.initialized:
                    raise FormatConversionError(
                        request_id=request_id,
                        source_format="clickhouse",
                        target_format="csv",
                        error_details="Connection manager is not initialized",
                        context={
                            "cluster_name": self.cluster_name,
                            "temp_file_path": temp_file_path,
                        },
                    )

                log.debug(f"Starting CSV streaming conversion for request {request_id}")

                # Determine output file extension based on compression
                file_extension = "zip" if options.compression else "csv"
                output_path = f"{temp_file_path}.{file_extension}"

                # Handle period separation if requested
                if (
                    options.separate_periods
                    and job_metadata
                    and job_metadata.available_periods
                ):
                    output_path = await self._convert_csv_with_period_separation(
                        query, options, temp_file_path, request_id, job_metadata, ctx
                    )
                else:
                    # Single CSV file conversion
                    output_path = await self._convert_single_csv(
                        query, options, output_path, request_id, job_metadata, ctx
                    )

                log.debug(f"Completed CSV streaming conversion: {output_path}")
                return output_path

            except (FormatConversionError, StreamingDataError):
                # Re-raise export-specific errors
                raise
            except Exception as e:
                raise FormatConversionError(
                    request_id=request_id,
                    source_format="clickhouse",
                    target_format="csv",
                    error_details=str(e),
                    context={
                        "cluster_name": self.cluster_name,
                        "temp_file_path": temp_file_path,
                        "error_type": type(e).__name__,
                    },
                ) from e

    async def convert_to_parquet_streaming(
        self,
        query: str,
        options: ExportOptions,
        temp_file_path: str,
        request_id: Optional[str] = None,
        job_metadata: Optional[JobMetadata] = None,
    ) -> str:
        """
        Convert streaming data to Parquet format.

        This method streams data directly from ClickHouse and writes it to
        Parquet format with chunked processing for memory efficiency.
        Supports period separation by creating separate files for each period.

        Args:
            query: SQL query to execute for data retrieval
            options: Export options including period separation
            temp_file_path: Path for temporary file storage
            request_id: Optional request ID for error tracking
            job_metadata: Optional job metadata for period information

        Returns:
            Path to the generated Parquet file (or ZIP file if multiple periods)

        Raises:
            FormatConversionError: If Parquet conversion fails
            StreamingDataError: If data streaming fails
        """
        request_id = request_id or str(uuid.uuid4())

        with ExportErrorContext(request_id, "parquet_conversion") as ctx:
            ctx.add_context("format", ExportFormat.PARQUET.value)
            ctx.add_context("separate_periods", options.separate_periods)

            try:
                if not self.connection_manager.initialized:
                    raise FormatConversionError(
                        request_id=request_id,
                        source_format="clickhouse",
                        target_format="parquet",
                        error_details="Connection manager is not initialized",
                        context={
                            "cluster_name": self.cluster_name,
                            "temp_file_path": temp_file_path,
                        },
                    )

                log.debug(
                    f"Starting Parquet streaming conversion for request {request_id}"
                )

                # Handle period separation if requested
                if (
                    options.separate_periods
                    and job_metadata
                    and job_metadata.available_periods
                ):
                    output_path = await self._convert_parquet_with_period_separation(
                        query, options, temp_file_path, request_id, job_metadata, ctx
                    )
                else:
                    # Single Parquet file conversion - check if extension already exists
                    if str(temp_file_path).endswith(".parquet"):
                        output_path = str(temp_file_path)
                    else:
                        output_path = f"{temp_file_path}.parquet"
                    output_path = await self._convert_single_parquet(
                        query, options, output_path, request_id, job_metadata, ctx
                    )

                log.debug(f"Completed Parquet streaming conversion: {output_path}")
                return output_path

            except (FormatConversionError, StreamingDataError):
                # Re-raise export-specific errors
                raise
            except Exception as e:
                raise FormatConversionError(
                    request_id=request_id,
                    source_format="clickhouse",
                    target_format="parquet",
                    error_details=str(e),
                    context={
                        "cluster_name": self.cluster_name,
                        "temp_file_path": temp_file_path,
                        "error_type": type(e).__name__,
                    },
                ) from e

    async def convert_to_excel_streaming(
        self,
        query: str,
        options: ExportOptions,
        temp_file_path: str,
        request_id: Optional[str] = None,
        job_metadata: Optional[JobMetadata] = None,
        job_info: Optional[Any] = None,
    ) -> str:
        """
        Convert streaming data to Excel format using ExcelStreamingWriter.

        This method uses the ExcelStreamingWriter for memory-efficient Excel
        file generation with support for both vertical and horizontal layouts,
        period separation, and Excel row limit validation.

        Args:
            query: SQL query to execute for data retrieval
            options: Export options including layout and period separation
            temp_file_path: Path for temporary file storage
            request_id: Optional request ID for error tracking
            job_metadata: Optional job metadata for optimization and validation
            job_info: Optional job information for info sheet

        Returns:
            Path to the generated Excel file

        Raises:
            FormatConversionError: If Excel conversion fails
            ExcelLimitExceededError: If data exceeds Excel limits
            StreamingDataError: If data streaming fails
        """
        request_id = request_id or str(uuid.uuid4())

        with ExportErrorContext(request_id, "excel_conversion") as ctx:
            ctx.add_context("format", ExportFormat.EXCEL.value)
            ctx.add_context("layout", options.excel_layout.value)
            ctx.add_context("separate_periods", options.separate_periods)

            try:
                if not self.connection_manager.initialized:
                    raise FormatConversionError(
                        request_id=request_id,
                        source_format="clickhouse",
                        target_format="excel",
                        error_details="Connection manager is not initialized",
                        context={
                            "cluster_name": self.cluster_name,
                            "temp_file_path": temp_file_path,
                        },
                    )

                # Validate job metadata is provided for Excel limits validation
                if not job_metadata:
                    raise FormatConversionError(
                        request_id=request_id,
                        source_format="clickhouse",
                        target_format="excel",
                        error_details="Job metadata is required for Excel conversion",
                        context={
                            "cluster_name": self.cluster_name,
                            "temp_file_path": temp_file_path,
                        },
                    )

                log.debug(
                    f"Starting Excel streaming conversion for request {request_id}"
                )
                log.info(
                    f"Format converter received job_info: {job_info is not None}, type: {type(job_info)}"
                )
                if job_info and isinstance(job_info, dict):
                    log.info(f"Job info has {len(job_info)} fields")
                    log.debug(f"Job info keys: {list(job_info.keys())}")
                else:
                    log.warning(
                        f"Job info is empty or invalid in format converter: {job_info}"
                    )

                # Create output path - check if extension already exists
                if str(temp_file_path).endswith(".xlsx"):
                    output_path = str(temp_file_path)
                else:
                    output_path = f"{temp_file_path}.xlsx"

                # Create Excel streaming writer
                excel_writer = ExcelStreamingWriter()

                # Check if we need to create separate Excel files for each period
                if (
                    options.separate_periods
                    and options.separate_periods_mode == PeriodSeparation.FILES
                    and job_metadata
                    and job_metadata.available_periods
                ):
                    # Create separate Excel files for each period
                    output_path = await self._convert_excel_with_period_separation(
                        query,
                        options,
                        temp_file_path,
                        request_id,
                        job_metadata,
                        job_info,
                        ctx,
                    )
                else:
                    # Single Excel file (with sheets if separate_periods_mode == SHEETS)
                    separate_sheets = (
                        options.separate_periods
                        and options.separate_periods_mode == PeriodSeparation.SHEETS
                    )

                    # Perform Excel streaming conversion with row limit validation
                    await excel_writer.write_streaming_excel_from_sqlalchemy(
                        connection_manager=self.connection_manager,
                        query=query,
                        output_path=output_path,
                        layout=options.excel_layout,
                        metadata=job_metadata,
                        separate_periods=separate_sheets,
                        job_info=job_info,
                        request_id=request_id,
                    )

                log.debug(f"Completed Excel streaming conversion: {output_path}")
                return output_path

            except (FormatConversionError, StreamingDataError):
                # Re-raise export-specific errors (including ExcelLimitExceededError)
                raise
            except Exception as e:
                raise FormatConversionError(
                    request_id=request_id,
                    source_format="clickhouse",
                    target_format="excel",
                    error_details=str(e),
                    context={
                        "cluster_name": self.cluster_name,
                        "temp_file_path": temp_file_path,
                        "error_type": type(e).__name__,
                        "layout": options.excel_layout.value
                        if options.excel_layout
                        else "unknown",
                    },
                ) from e

    async def _convert_single_csv(
        self,
        query: str,
        options: ExportOptions,
        output_path: str,
        request_id: str,
        job_metadata: Optional[JobMetadata] = None,
        ctx: Optional[ExportErrorContext] = None,
    ) -> str:
        """
        Convert data to a single CSV file with optional compression using chunked processing.

        This method uses pagination (LIMIT/OFFSET) to process large datasets in chunks,
        preventing excessive memory usage that would occur when loading millions of rows at once.

        Args:
            query: SQL query to execute
            options: Export options
            output_path: Output file path
            request_id: Request ID for error tracking

        Returns:
            Path to the generated file
        """
        rows_processed = 0
        headers_written = False
        # Use configured chunk size, defaulting to 50K for CSV exports (smaller than default 100K for memory efficiency)
        chunk_size = (
            getattr(settings, "CHUNK_SIZE_DEFAULT_ROWS", 100000) // 2
        )  # Start with half the default
        chunk_size = max(
            MIN_CHUNK_SIZE, min(MAX_CHUNK_SIZE, chunk_size)
        )  # Ensure within bounds
        offset = 0
        chunk_count = 0
        initial_memory_mb = get_memory_usage_mb()

        # Memory monitoring configuration
        max_memory_mb = getattr(
            settings, "LARGE_DATASET_MAX_MEMORY_MB", MEMORY_THRESHOLD_MB
        )
        chunks_since_last_adjustment = 0  # Track chunks since last size adjustment

        log.debug(
            f"Starting CSV conversion with chunk_size={chunk_size}, "
            f"memory_threshold={max_memory_mb}MB, initial_memory={initial_memory_mb:.1f}MB"
        )

        try:
            # Prepare file handles based on compression option
            if options.compression:
                # Create ZIP file with CSV inside, enable ZIP64 for large files
                zip_file = zipfile.ZipFile(
                    output_path,
                    "w",
                    zipfile.ZIP_DEFLATED,
                    allowZip64=True,  # Enable ZIP64 to handle files > 4GB
                )
                csv_filename = Path(output_path).stem + ".csv"
                csv_file = io.TextIOWrapper(
                    zip_file.open(csv_filename, "w"), encoding="utf-8", newline=""
                )
            else:
                # Direct CSV file
                csv_file = open(output_path, "w", encoding="utf-8", newline="")
                zip_file = None

            csv_writer = csv.writer(csv_file)

            # Stream data from ClickHouse using chunked processing
            async with self.connection_manager.connection() as conn:
                log.debug(
                    f"Starting chunked CSV processing with chunk size {chunk_size}"
                )

                while True:
                    # Store the current chunk size for this iteration (before potential adjustment)
                    current_iteration_chunk_size = chunk_size

                    # Add LIMIT and OFFSET to the query for pagination
                    paginated_query = (
                        f"{query} LIMIT {current_iteration_chunk_size} OFFSET {offset}"
                    )

                    # Execute paginated query in thread pool since ClickHouse driver is synchronous
                    result = await asyncio.get_event_loop().run_in_executor(
                        None, conn.execute, paginated_query
                    )

                    if not result:
                        # No more data
                        if offset == 0:
                            log.warning(
                                f"No data returned for query in request {request_id}"
                            )
                            # Still create empty file with headers if possible
                            if hasattr(conn, "last_query") and hasattr(
                                conn.last_query, "columns_with_types"
                            ):
                                headers = [
                                    col[0] for col in conn.last_query.columns_with_types
                                ]
                                csv_writer.writerow(headers)
                        break

                    # Write headers only for the first chunk
                    if not headers_written:
                        # Determine if this is a horizontal facts (pivot) query
                        is_pivot = (
                            options.horizontal_facts
                            and job_metadata
                            and job_metadata.facts_list
                            and job_metadata.job_info
                            and job_metadata.job_info.get("axes")
                        )

                        # Try to get column names using standardized utility first
                        headers = get_standardized_column_names(
                            connection_result=conn,
                            facts_list=job_metadata.facts_list if is_pivot else None,
                            axes_info=job_metadata.job_info.get("axes", {})
                            if is_pivot
                            else None,
                            is_pivot_query=is_pivot,
                            table_name=job_metadata.table_name
                            if job_metadata
                            else None,
                        )

                        # If standardized utility didn't work, try original method
                        if not headers:
                            if hasattr(conn, "last_query") and hasattr(
                                conn.last_query, "columns_with_types"
                            ):
                                headers = [
                                    col[0] for col in conn.last_query.columns_with_types
                                ]

                        # If still no headers, try schema retrieval as final fallback
                        if not headers and job_metadata and job_metadata.table_name:
                            try:
                                log.info(
                                    f"CSV converter using schema retrieval for {job_metadata.table_name}"
                                )
                                headers = await self._get_actual_column_names(
                                    job_metadata.table_name,
                                    ctx.request_id if ctx else request_id,
                                )
                            except Exception as schema_error:
                                log.warning(
                                    f"Schema retrieval failed for CSV export: {schema_error}"
                                )

                        # If all methods failed, raise error
                        if not headers:
                            raise ExportError(
                                message="Failed to retrieve column headers for CSV export",
                                error_type="csv_header_retrieval_failed",
                                request_id=ctx.request_id if ctx else request_id,
                                context={
                                    "table_name": job_metadata.table_name
                                    if job_metadata
                                    else "unknown",
                                    "has_result_data": bool(result),
                                    "result_columns": len(result[0]) if result else 0,
                                },
                                recovery_suggestions=[
                                    "Verify the table exists and has proper column metadata",
                                    "Check database connection and permissions",
                                    "Try a different export format",
                                    "Contact support if the issue persists",
                                ],
                            )

                        if headers:
                            csv_writer.writerow(headers)
                            headers_written = True

                    # Write data rows from this chunk
                    chunk_rows = 0
                    for row in result:
                        csv_writer.writerow(row)
                        rows_processed += 1
                        chunk_rows += 1

                    chunk_count += 1
                    chunks_since_last_adjustment += 1

                    # Monitor memory usage only on first chunk and immediately after adjustments
                    should_check_memory = (
                        chunk_count == 1  # First chunk of processing
                        or chunks_since_last_adjustment
                        == 1  # First chunk after adjustment
                    )

                    if should_check_memory:
                        current_memory_mb = get_memory_usage_mb()
                        memory_increase_mb = current_memory_mb - initial_memory_mb

                        # Log memory usage
                        log.info(
                            f"CSV Export Memory Check - Processed: {rows_processed:,} rows, "
                            f"Memory: {current_memory_mb:.1f}MB (+{memory_increase_mb:.1f}MB), "
                            f"Chunk size: {chunk_size}"
                        )

                        # Adjust chunk size based on memory usage (for next iteration)
                        new_chunk_size = monitor_memory_and_adjust_chunk_size(
                            chunk_size, current_memory_mb, max_memory_mb
                        )

                        if new_chunk_size != chunk_size:
                            chunk_size = new_chunk_size
                            chunks_since_last_adjustment = 0  # Reset counter
                            log.info(
                                f"Adjusted CSV chunk size to {chunk_size} rows for next iteration"
                            )

                        # Emergency memory check - if memory usage is extremely high, force smaller chunks
                        if current_memory_mb > max_memory_mb * 1.5:  # 150% of threshold
                            chunk_size = max(MIN_CHUNK_SIZE, chunk_size // 2)
                            chunks_since_last_adjustment = 0  # Reset counter
                            log.warning(
                                f"Emergency memory reduction: forcing chunk size to {chunk_size} "
                                f"(memory: {current_memory_mb:.1f}MB > {max_memory_mb * 1.5:.1f}MB)"
                            )

                    # Log progress for large datasets
                    if rows_processed % 100000 == 0:
                        current_memory_mb = get_memory_usage_mb()
                        log.info(
                            f"Processed {rows_processed:,} rows for CSV conversion "
                            f"(chunk {chunk_count}, memory: {current_memory_mb:.1f}MB)"
                        )

                    # Check if we've reached the end: compare against the chunk size used for THIS query
                    if chunk_rows < current_iteration_chunk_size:
                        log.debug(
                            f"Reached end of data: chunk had {chunk_rows} rows "
                            f"(< {current_iteration_chunk_size} requested)"
                        )
                        break

                    # Move to next chunk (use the chunk size that was actually used for this iteration)
                    offset += current_iteration_chunk_size

                    # Yield control to allow other tasks to run
                    await asyncio.sleep(0)

            # Close files properly
            csv_file.close()
            if zip_file:
                # Add job info file to ZIP if job_metadata is available
                if job_metadata:
                    try:
                        # Create temporary job info file
                        temp_info_path = f"{output_path}_temp_job_info.json"
                        self._create_csv_job_info_file(
                            job_metadata, temp_info_path, options
                        )
                        zip_file.write(temp_info_path, "job_info.json")
                        Path(temp_info_path).unlink(missing_ok=True)
                        log.debug("Added job info file to CSV ZIP archive")
                    except Exception as e:
                        log.warning(f"Failed to add job info to CSV ZIP: {e}")
                zip_file.close()

            # Final memory usage report
            final_memory_mb = get_memory_usage_mb()
            total_memory_increase_mb = final_memory_mb - initial_memory_mb

            log.info(
                f"CSV conversion completed: {rows_processed:,} rows written to {output_path}, "
                f"processed {chunk_count} chunks, "
                f"final memory: {final_memory_mb:.1f}MB (+{total_memory_increase_mb:.1f}MB)"
            )
            return output_path

        except Exception as e:
            # Ensure files are closed on error
            try:
                if "csv_file" in locals():
                    csv_file.close()
                if "zip_file" in locals() and zip_file:
                    zip_file.close()
            except:
                pass

            # Get final memory usage for error context
            error_memory_mb = get_memory_usage_mb()
            memory_increase_mb = (
                error_memory_mb - initial_memory_mb
                if "initial_memory_mb" in locals()
                else 0
            )

            raise StreamingDataError(
                request_id=request_id,
                operation="csv_streaming",
                error_details=str(e),
                rows_processed=rows_processed,
                context={
                    "output_path": output_path,
                    "headers_written": headers_written,
                    "compression": options.compression,
                    "chunk_size": chunk_size,
                    "last_offset": offset,
                    "chunks_processed": chunk_count if "chunk_count" in locals() else 0,
                    "initial_memory_mb": initial_memory_mb
                    if "initial_memory_mb" in locals()
                    else 0,
                    "error_memory_mb": error_memory_mb,
                    "memory_increase_mb": memory_increase_mb,
                    "max_memory_threshold_mb": max_memory_mb
                    if "max_memory_mb" in locals()
                    else 0,
                },
            ) from e

    async def _convert_single_parquet(
        self,
        query: str,
        options: ExportOptions,
        output_path: str,
        request_id: str,
        job_metadata: Optional[JobMetadata] = None,
        ctx: Optional[ExportErrorContext] = None,
    ) -> str:
        """
        Convert data to a single Parquet file using ClickHouse HTTP interface.

        This method leverages ClickHouse's native Parquet export capability
        through the HTTP interface for optimal performance and memory efficiency.
        It also embeds job metadata in the Parquet file for comprehensive information.

        Args:
            query: SQL query to execute
            options: Export options
            output_path: Output file path
            request_id: Request ID for error tracking
            job_metadata: Optional job metadata for embedding in Parquet file

        Returns:
            Path to the generated Parquet file
        """
        try:
            # Use the connection pool adapter for Parquet streaming
            # This leverages ClickHouse's native Parquet export capability
            from magic_gateway.export.adapters.connection_pool import (
                ConnectionPoolAdapter,
            )

            pool_adapter = ConnectionPoolAdapter()

            # Generate a unique query ID for tracking
            query_id = f"parquet_export_{request_id}_{uuid.uuid4().hex[:8]}"

            log.debug(
                f"Starting Parquet export using HTTP interface for query {query_id}"
            )

            # Stream Parquet data directly to file
            bytes_written = 0
            with open(output_path, "wb") as parquet_file:
                async for chunk in pool_adapter.export_to_parquet_stream(
                    query=query,
                    connection_manager=self.connection_manager,
                    query_id=query_id,
                    request_id=request_id,
                ):
                    parquet_file.write(chunk)
                    bytes_written += len(chunk)

                    # Log progress for large files
                    if bytes_written % (50 * 1024 * 1024) == 0:  # Every 50MB
                        log.debug(
                            f"Written {bytes_written / (1024 * 1024):.1f} MB to Parquet file"
                        )

            log.debug(
                f"Parquet conversion completed: {bytes_written / (1024 * 1024):.1f} MB written to {output_path}"
            )

            # Add metadata to Parquet file if job_metadata is available
            if job_metadata:
                try:
                    log.debug(
                        f"Adding metadata to Parquet file for job {job_metadata.job_id}"
                    )
                    await self._add_metadata_to_parquet(
                        output_path, job_metadata, options
                    )
                    log.debug("Metadata successfully added to Parquet file")
                except Exception as e:
                    log.warning(
                        f"Failed to add metadata to Parquet file: {e}", exc_info=True
                    )
                    # Don't fail the export if metadata addition fails

            return output_path

        except Exception as e:
            raise StreamingDataError(
                request_id=request_id,
                operation="parquet_streaming",
                error_details=str(e),
                context={
                    "output_path": output_path,
                    "bytes_written": bytes_written
                    if "bytes_written" in locals()
                    else 0,
                },
            ) from e

    async def _convert_csv_with_period_separation(
        self,
        query: str,
        options: ExportOptions,
        temp_file_path: str,
        request_id: str,
        job_metadata: JobMetadata,
        ctx: Optional[ExportErrorContext] = None,
    ) -> str:
        """
        Convert data to separate CSV files for each period.

        Args:
            query: Base SQL query
            options: Export options
            temp_file_path: Base path for temporary files
            request_id: Request ID for error tracking
            job_metadata: Job metadata containing period information

        Returns:
            Path to ZIP file containing all period CSV files and job info
        """
        zip_path = f"{temp_file_path}_periods.zip"

        try:
            with zipfile.ZipFile(
                zip_path, "w", zipfile.ZIP_DEFLATED, allowZip64=True
            ) as zip_file:
                # Create job info file for CSV export
                job_info_path = f"{temp_file_path}_job_info.json"
                self._create_csv_job_info_file(job_metadata, job_info_path, options)
                zip_file.write(job_info_path, "job_info.json")
                Path(job_info_path).unlink(missing_ok=True)

                for period in job_metadata.available_periods:
                    log.debug(f"Processing period '{period}' for CSV export")

                    # Add period filter to query using standardized utility
                    period_query = add_period_filter_to_query(query, period)

                    # Create temporary CSV for this period
                    period_csv_path = f"{temp_file_path}_{period}.csv"

                    # Convert this period's data to CSV
                    period_options = ExportOptions(
                        format=options.format,
                        compression=False,  # Don't compress individual files
                        separate_periods=False,
                        horizontal_facts=options.horizontal_facts,
                        excel_layout=options.excel_layout,
                    )

                    await self._convert_single_csv(
                        period_query,
                        period_options,
                        period_csv_path,
                        request_id,
                        job_metadata,
                        ctx,
                    )

                    # Add to ZIP file
                    csv_filename = f"{period}.csv"
                    zip_file.write(period_csv_path, csv_filename)

                    # Clean up temporary file
                    Path(period_csv_path).unlink(missing_ok=True)

            log.debug(f"Created period-separated CSV ZIP with job info: {zip_path}")
            return zip_path

        except Exception as e:
            # Clean up any temporary files
            for period in job_metadata.available_periods:
                period_csv_path = f"{temp_file_path}_{period}.csv"
                Path(period_csv_path).unlink(missing_ok=True)

            job_info_path = f"{temp_file_path}_job_info.json"
            Path(job_info_path).unlink(missing_ok=True)

            raise FormatConversionError(
                request_id=request_id,
                source_format="clickhouse",
                target_format="csv_periods",
                error_details=str(e),
                context={
                    "periods": job_metadata.available_periods,
                    "zip_path": zip_path,
                },
            ) from e

    async def _convert_parquet_with_period_separation(
        self,
        query: str,
        options: ExportOptions,
        temp_file_path: str,
        request_id: str,
        job_metadata: JobMetadata,
        ctx: Optional[ExportErrorContext] = None,
    ) -> str:
        """
        Convert data to separate Parquet files for each period.

        Args:
            query: Base SQL query
            options: Export options
            temp_file_path: Base path for temporary files
            request_id: Request ID for error tracking
            job_metadata: Job metadata containing period information

        Returns:
            Path to ZIP file containing all period Parquet files
        """
        zip_path = f"{temp_file_path}_periods.zip"

        try:
            with zipfile.ZipFile(
                zip_path, "w", zipfile.ZIP_DEFLATED, allowZip64=True
            ) as zip_file:
                for period in job_metadata.available_periods:
                    log.debug(f"Processing period '{period}' for Parquet export")

                    # Add period filter to query using standardized utility
                    period_query = add_period_filter_to_query(query, period)

                    # Create temporary Parquet for this period
                    period_parquet_path = f"{temp_file_path}_{period}.parquet"

                    # Convert this period's data to Parquet
                    await self._convert_single_parquet(
                        period_query,
                        options,
                        period_parquet_path,
                        request_id,
                        None,
                        ctx,
                    )

                    # Add to ZIP file
                    parquet_filename = f"{period}.parquet"
                    zip_file.write(period_parquet_path, parquet_filename)

                    # Clean up temporary file
                    Path(period_parquet_path).unlink(missing_ok=True)

            log.debug(f"Created period-separated Parquet ZIP: {zip_path}")
            return zip_path

        except Exception as e:
            # Clean up any temporary files
            for period in job_metadata.available_periods:
                period_parquet_path = f"{temp_file_path}_{period}.parquet"
                Path(period_parquet_path).unlink(missing_ok=True)

            raise FormatConversionError(
                request_id=request_id,
                source_format="clickhouse",
                target_format="parquet_periods",
                error_details=str(e),
                context={
                    "periods": job_metadata.available_periods,
                    "zip_path": zip_path,
                },
            ) from e

    async def _convert_excel_with_period_separation(
        self,
        query: str,
        options: ExportOptions,
        temp_file_path: str,
        request_id: str,
        job_metadata: JobMetadata,
        job_info: Optional[Any] = None,
        ctx: Optional[ExportErrorContext] = None,
    ) -> str:
        """
        Convert data to separate Excel files for each period.

        Args:
            query: Base SQL query
            options: Export options
            temp_file_path: Base path for temporary files
            request_id: Request ID for error tracking
            job_metadata: Job metadata containing period information
            job_info: Optional job information for info sheets

        Returns:
            Path to ZIP file containing all period Excel files
        """
        zip_path = f"{temp_file_path}_periods.zip"

        try:
            with zipfile.ZipFile(
                zip_path, "w", zipfile.ZIP_DEFLATED, allowZip64=True
            ) as zip_file:
                for period in job_metadata.available_periods:
                    log.debug(f"Processing period '{period}' for Excel export")

                    # Add period filter to query using standardized utility
                    period_query = add_period_filter_to_query(query, period)

                    # Create temporary Excel for this period
                    period_excel_path = f"{temp_file_path}_{period}.xlsx"

                    # Create Excel streaming writer for this period
                    excel_writer = ExcelStreamingWriter()

                    # Convert this period's data to Excel
                    await excel_writer.write_streaming_excel_from_sqlalchemy(
                        connection_manager=self.connection_manager,
                        query=period_query,
                        output_path=period_excel_path,
                        layout=options.excel_layout,
                        metadata=job_metadata,
                        separate_periods=False,  # Single period per file
                        job_info=job_info,
                        request_id=request_id,
                    )

                    # Add to ZIP file
                    excel_filename = f"{period}.xlsx"
                    zip_file.write(period_excel_path, excel_filename)

                    # Clean up temporary file
                    Path(period_excel_path).unlink(missing_ok=True)

            log.debug(f"Created period-separated Excel ZIP: {zip_path}")
            return zip_path

        except Exception as e:
            # Clean up any temporary files
            for period in job_metadata.available_periods:
                period_excel_path = f"{temp_file_path}_{period}.xlsx"
                Path(period_excel_path).unlink(missing_ok=True)

            raise FormatConversionError(
                request_id=request_id,
                source_format="clickhouse",
                target_format="excel_periods",
                error_details=str(e),
                context={
                    "periods": job_metadata.available_periods,
                    "zip_path": zip_path,
                },
            ) from e

    def _register_temp_file(self, file_path: str, request_id: str) -> None:
        """
        Register a temporary file with the temp file manager.

        Args:
            file_path: Path to the temporary file
            request_id: Request ID for tracking
        """
        try:
            # Register with temp file manager for automatic cleanup
            temp_file_manager.save_result_file(
                content="",  # Empty content since file already exists
                file_id=request_id,
                extension=Path(file_path).suffix.lstrip("."),
                prefix="export_",
            )
        except Exception as e:
            log.warning(f"Failed to register temp file {file_path}: {e}")

    async def _add_metadata_to_parquet(
        self, parquet_path: str, job_metadata: JobMetadata, options: ExportOptions
    ) -> None:
        """
        Add job metadata to Parquet file using PyArrow.

        This method reads the existing Parquet file, adds metadata, and rewrites it
        with the enhanced metadata information for comprehensive job information.

        Args:
            parquet_path: Path to the Parquet file
            job_metadata: JobMetadata object containing job information
            options: Export options for context
        """
        try:
            import pyarrow.parquet as pq

            # Generate metadata using InfoSheetGenerator
            info_generator = InfoSheetGenerator(None)  # No workbook needed
            layout = (
                ExcelLayout.HORIZONTAL
                if options.horizontal_facts
                else ExcelLayout.VERTICAL
            )
            metadata_dict = info_generator.generate_metadata_for_export(
                job_metadata, parquet_path, layout
            )

            # Read the existing Parquet file
            table = pq.read_table(parquet_path)

            # Convert metadata to bytes for PyArrow
            metadata_bytes = {k.encode(): v.encode() for k, v in metadata_dict.items()}

            # Create a new table with the same data but with metadata
            new_table = table.replace_schema_metadata(metadata_bytes)

            # Write the table back to the file with metadata
            pq.write_table(new_table, parquet_path)

            log.debug(f"Added {len(metadata_dict)} metadata fields to Parquet file")

        except ImportError:
            log.warning("PyArrow not available, skipping Parquet metadata addition")
        except Exception as e:
            log.warning(f"Failed to add metadata to Parquet file: {e}", exc_info=True)
            # Don't fail the export if metadata addition fails

    def _create_csv_job_info_file(
        self, job_metadata: JobMetadata, info_file_path: str, options: ExportOptions
    ) -> None:
        """
        Create a job info file for CSV exports.

        This method creates a JSON file containing job metadata and information
        similar to what's included in Excel info sheets.

        Args:
            job_metadata: JobMetadata object containing job information
            info_file_path: Path where the job info file should be created
            options: Export options for context
        """
        try:
            # Generate metadata using InfoSheetGenerator
            info_generator = InfoSheetGenerator(None)  # No workbook needed
            layout = (
                ExcelLayout.HORIZONTAL
                if options.horizontal_facts
                else ExcelLayout.VERTICAL
            )

            # Get the filename from the path
            filename = Path(info_file_path).stem

            metadata_dict = info_generator.generate_metadata_for_export(
                job_metadata, filename, layout
            )

            # Add CSV-specific information
            csv_info = {
                **metadata_dict,
                "export_format": "CSV",
                "horizontal_facts": options.horizontal_facts,
                "separate_periods": options.separate_periods,
                "compression": options.compression,
                "export_timestamp": job_metadata.created_at.isoformat()
                if job_metadata.created_at
                else "",
                "total_periods": len(job_metadata.available_periods)
                if job_metadata.available_periods
                else 0,
            }

            # Write the job info as JSON
            with open(info_file_path, "w", encoding="utf-8") as f:
                import json

                json.dump(csv_info, f, indent=2, ensure_ascii=False)

            log.debug(
                f"Created CSV job info file with {len(csv_info)} fields: {info_file_path}"
            )

        except Exception as e:
            log.error(f"Failed to create CSV job info file: {e}", exc_info=True)
            # Raise error instead of creating fallback file
            raise ExportError(
                message="Failed to create CSV job info file",
                error_type="csv_job_info_creation_failed",
                request_id=None,  # Will be set by context manager if available
                context={
                    "job_id": job_metadata.job_id,
                    "info_file_path": str(info_file_path),
                    "error": str(e),
                },
                recovery_suggestions=[
                    "Check file system permissions for the export directory",
                    "Verify sufficient disk space is available",
                    "Try the export without job info file generation",
                    "Contact support if the issue persists",
                ],
            )

    async def _get_actual_column_names(
        self, table_name: str, request_id: Optional[str] = None
    ) -> List[str]:
        """
        Get actual column names from the ClickHouse table schema.

        This method queries the ClickHouse system.columns table to retrieve the real
        column names and types from the dataset source instead of using hardcoded defaults.

        Args:
            table_name: Fully qualified table name (database.table)
            request_id: Optional request ID for tracking

        Returns:
            List of actual column names from the table schema

        Raises:
            StreamingDataError: If column names cannot be retrieved
        """
        try:
            log.info(
                f"CSV converter retrieving actual column names for table {table_name}"
            )

            # Use the ClickHouseHandler to get table schema
            from magic_gateway.db.clickhouse_handler import ClickHouseHandler

            schema_info = await ClickHouseHandler.get_table_schema(
                table_name=table_name,
                query_id=request_id,
            )

            if not schema_info:
                raise StreamingDataError(
                    request_id=request_id or "unknown",
                    operation="get_column_names_csv",
                    error_details=f"No schema information found for table {table_name}",
                    context={
                        "table_name": table_name,
                        "request_id": request_id,
                        "operation": "get_column_names_csv",
                    },
                )

            # Extract column names in order
            column_names = [col["name"] for col in schema_info]

            log.info(
                f"CSV converter retrieved {len(column_names)} actual column names for {table_name}"
            )
            return column_names

        except Exception as e:
            log.error(
                f"Failed to retrieve column names for table {table_name}: {str(e)}",
                exc_info=True,
            )
            raise StreamingDataError(
                request_id=request_id or "unknown",
                operation="get_column_names_csv",
                error_details=f"Failed to retrieve column names for table {table_name}: {str(e)}",
                context={
                    "table_name": table_name,
                    "request_id": request_id,
                    "operation": "get_column_names_csv",
                    "error": str(e),
                },
            ) from e
