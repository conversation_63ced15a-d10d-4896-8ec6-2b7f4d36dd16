"""
Query utilities for export operations.

This module provides common query manipulation utilities used across
different export formats to ensure consistent behavior.
"""

from typing import Optional, List, Dict, Any

from magic_gateway.export.exceptions import ExportError


def add_period_filter_to_query(query: str, period: str) -> str:
    """
    Add period filter to a SQL query, handling WHERE, GROUP BY, and ORDER BY clauses correctly.

    This method ensures consistent period filtering across CSV and Excel export formats by properly
    injecting the WHERE clause after existing conditions but before GROUP BY and ORDER BY.
    This standardized approach ensures that CSV and Excel exports use the same period-based
    data filtering logic, while Parquet maintains its own implementation.

    Args:
        query: Original SQL query
        period: Period value to filter by

    Returns:
        Modified query with period filter inserted at the correct position

    Examples:
        >>> add_period_filter_to_query("SELECT * FROM table", "2024-01")
        "SELECT * FROM table WHERE period_name = '2024-01'"

        >>> add_period_filter_to_query("SELECT * FROM table WHERE id > 0 ORDER BY id", "2024-01")
        "SELECT * FROM table WHERE id > 0 AND period_name = '2024-01' ORDER BY id"

        >>> add_period_filter_to_query("SELECT * FROM table GROUP BY col ORDER BY col", "2024-01")
        "SELECT * FROM table WHERE period_name = '2024-01' GROUP BY col ORDER BY col"
    """
    escaped_period = _escape_sql_string(period)
    query_upper = query.upper()

    if "WHERE" in query_upper:
        # Add to existing WHERE clause
        return f"{query} AND period_name = '{escaped_period}'"
    else:
        # Need to insert WHERE clause before GROUP BY or ORDER BY
        group_by_index = query_upper.find("GROUP BY")
        order_by_index = query_upper.find("ORDER BY")

        # Find the earliest clause that should come after WHERE
        insert_index = len(query)  # Default to end of query
        insert_clause = ""

        if group_by_index != -1:
            insert_index = group_by_index
            insert_clause = query[group_by_index:]
        elif order_by_index != -1:
            insert_index = order_by_index
            insert_clause = query[order_by_index:]

        # Insert WHERE clause at the correct position
        before_clause = query[:insert_index].strip()
        if insert_clause:
            return f"{before_clause} WHERE period_name = '{escaped_period}' {insert_clause}"
        else:
            return f"{before_clause} WHERE period_name = '{escaped_period}'"


def get_standardized_column_names(
    connection_result=None,
    facts_list: Optional[List[str]] = None,
    axes_info: Optional[Dict[str, Any]] = None,
    is_pivot_query: bool = False,
    table_name: Optional[str] = None,
) -> List[str]:
    """
    Get standardized column names for consistent naming across all export formats.

    This function ensures that CSV, Excel, and Parquet exports use the same
    column naming conventions, providing a consistent user experience.

    Args:
        connection_result: ClickHouse connection result with column information
        facts_list: List of fact names for pivot queries
        axes_info: Axes information for pivot queries
        is_pivot_query: Whether this is a horizontal facts pivot query

        table_name: Table name for schema retrieval (optional)

    Returns:
        List of standardized column names
    """
    try:
        if is_pivot_query and facts_list and axes_info:
            # For pivot queries, construct column names from transformation logic
            return _get_pivot_column_names(facts_list, axes_info)

        # Try to get column names from connection result's last_query
        if (
            connection_result
            and hasattr(connection_result, "last_query")
            and hasattr(connection_result.last_query, "columns_with_types")
        ):
            column_names = [
                col[0] for col in connection_result.last_query.columns_with_types
            ]
            if column_names:
                return column_names

        # For non-pivot queries, we need to return None to indicate that
        # the caller should use schema retrieval or other methods
        # Don't fall back to generic names immediately
        return []

    except Exception as e:
        # Raise error instead of returning empty list
        raise ExportError(
            message="Failed to retrieve standardized column names",
            error_type="column_name_standardization_failed",
            request_id=None,  # Will be set by context manager if available
            context={
                "table_name": table_name,
                "is_pivot_query": is_pivot_query,
                "has_facts_list": facts_list is not None,
                "has_axes_info": axes_info is not None,
                "error": str(e),
            },
            recovery_suggestions=[
                "Verify the table exists and has proper column metadata",
                "Check database connection and permissions",
                "Ensure facts list and axes info are properly formatted",
                "Contact support if the issue persists",
            ],
        )


def _get_pivot_column_names(
    facts_list: List[str], axes_info: Dict[str, Any]
) -> List[str]:
    """
    Construct standardized column names for horizontal facts pivot queries.

    This method builds the column names that will be produced by the pivot transformation,
    ensuring consistency across all export formats.

    Args:
        facts_list: List of fact names to pivot into columns
        axes_info: Axes information from metadata for column names

    Returns:
        List of column names in the order they appear in pivot query results
    """
    try:
        column_names = []

        # Add axis columns and position columns (same logic as in streaming retriever)
        for axis_key, axis_name in axes_info.items():
            # Extract the base axis name before any metadata in parentheses
            if isinstance(axis_name, str) and " (" in axis_name:
                axis_name = axis_name.split(" (")[0].strip()
            elif isinstance(axis_name, str) and "." in axis_name:
                axis_name = axis_name.split(".")[1].strip()[5:]
            elif isinstance(axis_name, str) and len(axis_name) > 5:
                # Only slice if it's a string with sufficient length
                axis_name = str(axis_name[5:])
            else:
                # Handle non-string values or short strings
                axis_name = str(axis_name)

            # Alternate axis names and position numbers for better readability
            for axis_pair in zip(
                [axis_key + "_" + axis_name], [f"{axis_name}_position_number"]
            ):
                column_names.extend(axis_pair)

        # Add period_name after axis columns
        column_names.extend(["period_name"])

        # Add fact columns (these become the pivoted columns)
        column_names.extend(facts_list)

        return column_names

    except Exception:
        # If anything fails, return basic structure
        return ["period_name"] + (facts_list or [])


def _escape_sql_string(value: str) -> str:
    """
    Escape SQL string values to prevent injection attacks.

    Args:
        value: String value to escape

    Returns:
        Escaped string safe for SQL queries
    """
    if not value:
        return ""
    # Escape single quotes by doubling them
    return value.replace("'", "''")
