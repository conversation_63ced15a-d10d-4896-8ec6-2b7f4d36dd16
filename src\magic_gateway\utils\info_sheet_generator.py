"""
Unified generator for Excel info sheets.

This module provides the InfoSheetGenerator class that consolidates all info sheet
generation logic and provides a consistent interface for all export formats.
"""

import json
from typing import Any, Dict, Optional, Union
import xlsxwriter

from magic_gateway.core.logging_config import log
from magic_gateway.export.models import JobMetadata, ExcelLayout


class InfoSheetGenerator:
    """
    Unified generator for Excel info sheets.

    This class consolidates all info sheet generation logic and provides
    a consistent interface for all export formats.
    """

    def __init__(self, workbook: Optional[xlsxwriter.Workbook]):
        """
        Initialize the info sheet generator.

        Args:
            workbook: The xlsxwriter Workbook instance to add the info sheet to.
                     Can be None if only using for data processing.
        """
        self.workbook = workbook

        # Create standard formats only if workbook is provided
        if workbook:
            self.header_format = workbook.add_format(
                {
                    "bold": True,
                    "font_name": "Arial",
                    "font_size": 10,
                    "bg_color": "#D8E4BC",
                    "border": 1,
                }
            )

            self.section_header_format = workbook.add_format(
                {
                    "bold": True,
                    "font_name": "Arial",
                    "font_size": 10,
                    "bg_color": "#E8F4DC",
                    "border": 1,
                }
            )
        else:
            self.header_format = None
            self.section_header_format = None

    def process_job_info(self, job_info: Any, filename: str) -> Dict[str, Any]:
        """
        Process job_info into a standardized dictionary format.

        Args:
            job_info: Raw job information (can be dict, string, or other)
            filename: The export filename

        Returns:
            Processed job information as a dictionary
        """
        processed_info = {}
        log.debug(
            f"Processing job_info: type={type(job_info)}, content={str(job_info)[:100]}"
        )

        # Handle string job_info (try to parse as JSON)
        if isinstance(job_info, str):
            try:
                # Try to parse as JSON
                parsed_job_info = json.loads(job_info)
                if isinstance(parsed_job_info, dict):
                    job_info = parsed_job_info
                else:
                    job_info = {"job_info": job_info}
            except json.JSONDecodeError:
                # If not valid JSON, keep as string
                job_info = {"job_info": job_info}

        # If job_info is a dictionary, process it
        if isinstance(job_info, dict):
            # Convert all values to strings, but handle nested dictionaries properly
            processed_info = {}
            for k, v in job_info.items():
                if isinstance(v, dict):
                    # Convert nested dictionaries to JSON strings for proper parsing later
                    processed_info[k] = json.dumps(v)
                else:
                    processed_info[k] = str(v)

            # Extract job_id from filename if not in job_info
            if "job_id" not in processed_info and filename and "job" in filename:
                try:
                    job_id_str = filename.split("_job")[-1]
                    if job_id_str.isdigit():
                        processed_info["job_id"] = job_id_str
                except Exception:
                    pass

            # Handle id_panel and panel_id mapping
            if "id_panel" in processed_info:
                processed_info["panel_id"] = processed_info["id_panel"]

            # Process axes information from multiple sources
            axes = {}

            # First, check for direct axes field
            axes_data = processed_info.get("axes", "")
            if axes_data:
                try:
                    if isinstance(axes_data, str):
                        axes = json.loads(axes_data)
                    else:
                        axes = axes_data
                except (json.JSONDecodeError, TypeError):
                    log.debug(f"Could not parse axes data: {axes_data}")

            # Also check for individual axis fields in the job_info
            for key, value in processed_info.items():
                if key.startswith("axis_") or key.endswith("_axis"):
                    axis_name = key.replace("axis_", "").replace("_axis", "")
                    if value and str(value).lower() not in ["none", "null", ""]:
                        axes[axis_name] = str(value)

            # Check for nested job_info that might contain axes
            if "job_info" in processed_info:
                try:
                    nested_job_info = processed_info["job_info"]
                    if isinstance(nested_job_info, str):
                        nested_data = json.loads(nested_job_info)
                    else:
                        nested_data = nested_job_info

                    if isinstance(nested_data, dict):
                        # Extract axes from nested job_info
                        nested_axes = nested_data.get("axes", {})
                        if isinstance(nested_axes, str):
                            nested_axes = json.loads(nested_axes)
                        if isinstance(nested_axes, dict):
                            axes.update(nested_axes)

                        # Also check for nested_axes field
                        nested_axes_alt = nested_data.get("nested_axes", {})
                        if isinstance(nested_axes_alt, dict):
                            axes.update(nested_axes_alt)

                        # Look for individual axis fields in nested data
                        for key, value in nested_data.items():
                            if key.startswith("axis_") or key.endswith("_axis"):
                                axis_name = key.replace("axis_", "").replace(
                                    "_axis", ""
                                )
                                if value and str(value).lower() not in [
                                    "none",
                                    "null",
                                    "",
                                ]:
                                    axes[axis_name] = str(value)
                except (json.JSONDecodeError, TypeError, AttributeError):
                    log.debug("Could not parse nested job_info for axes")

            if axes and isinstance(axes, dict):
                processed_info["axes"] = json.dumps(axes)

            # Process filters information from multiple sources
            filters = {}

            # First, check for direct filters field
            filters_data = processed_info.get("filters", "")
            if filters_data:
                try:
                    if isinstance(filters_data, str):
                        filters_dict = json.loads(filters_data)
                    else:
                        filters_dict = filters_data

                    if isinstance(filters_dict, dict):
                        for filter_key, filter_value in filters_dict.items():
                            if filter_value is not None and str(
                                filter_value
                            ).lower() not in ["none", "null", ""]:
                                filter_name = filter_key.replace("_filter", "")
                                if "FilterData" in str(filter_value) or "<class" in str(
                                    filter_value
                                ):
                                    filters[filter_name] = "Applied"
                                else:
                                    filters[filter_name] = str(filter_value)
                except (json.JSONDecodeError, TypeError):
                    log.debug(f"Could not parse filters data: {filters_data}")

            # Also check for individual filter fields (but skip nested ones)
            for key, value in processed_info.items():
                if (
                    "filter" in key.lower()
                    and key != "filters"
                    and key != "nested_filters"
                    and not key.startswith("nested_")
                ):
                    filter_name = key.replace("_filter", "").replace("filter_", "")
                    if value and str(value).lower() not in ["none", "null", ""]:
                        if "FilterData" in str(value) or "<class" in str(value):
                            filters[filter_name] = "Applied"
                        else:
                            filters[filter_name] = str(value)

            # Check for nested job_info that might contain filters
            if "job_info" in processed_info:
                try:
                    nested_job_info = processed_info["job_info"]
                    if isinstance(nested_job_info, str):
                        nested_data = json.loads(nested_job_info)
                    else:
                        nested_data = nested_job_info

                    if isinstance(nested_data, dict):
                        # Extract filters from nested job_info
                        nested_filters = nested_data.get("filters", {})
                        if isinstance(nested_filters, str):
                            nested_filters = json.loads(nested_filters)
                        if isinstance(nested_filters, dict):
                            for filter_key, filter_value in nested_filters.items():
                                if filter_value is not None and str(
                                    filter_value
                                ).lower() not in ["none", "null", ""]:
                                    filter_name = filter_key.replace("_filter", "")
                                    if "FilterData" in str(
                                        filter_value
                                    ) or "<class" in str(filter_value):
                                        filters[filter_name] = "Applied"
                                    else:
                                        filters[filter_name] = str(filter_value)

                        # Also check for nested_filters field
                        nested_filters_alt = nested_data.get("nested_filters", {})
                        if isinstance(nested_filters_alt, dict):
                            for filter_key, filter_value in nested_filters_alt.items():
                                if filter_value is not None and str(
                                    filter_value
                                ).lower() not in ["none", "null", ""]:
                                    filter_name = filter_key.replace("_filter", "")
                                    if "FilterData" in str(
                                        filter_value
                                    ) or "<class" in str(filter_value):
                                        filters[filter_name] = "Applied"
                                    else:
                                        filters[filter_name] = str(filter_value)

                        # Look for individual filter fields in nested data (but skip nested_filters)
                        for key, value in nested_data.items():
                            if (
                                "filter" in key.lower()
                                and key != "filters"
                                and key != "nested_filters"
                                and not isinstance(value, dict)
                            ):
                                filter_name = key.replace("_filter", "").replace(
                                    "filter_", ""
                                )
                                if value and str(value).lower() not in [
                                    "none",
                                    "null",
                                    "",
                                ]:
                                    if "FilterData" in str(value) or "<class" in str(
                                        value
                                    ):
                                        filters[filter_name] = "Applied"
                                    else:
                                        filters[filter_name] = str(value)
                except (json.JSONDecodeError, TypeError, AttributeError):
                    log.debug("Could not parse nested job_info for filters")

            if filters:
                processed_info["filters"] = json.dumps(filters)

            # Process periods information from multiple sources
            periods = []

            # First, check for direct periods field
            periods_data = processed_info.get("periods", "")
            if periods_data:
                try:
                    if isinstance(periods_data, str):
                        # Try to parse as JSON first
                        try:
                            parsed_periods = json.loads(periods_data)
                            if isinstance(parsed_periods, list):
                                periods.extend(parsed_periods)
                            else:
                                periods.append(str(parsed_periods))
                        except json.JSONDecodeError:
                            # If not JSON, check if it's a Python list string or Period objects
                            if periods_data.startswith("[") and periods_data.endswith(
                                "]"
                            ):
                                import ast
                                import re

                                try:
                                    parsed_list = ast.literal_eval(periods_data)
                                    if isinstance(parsed_list, list):
                                        periods.extend(parsed_list)
                                    else:
                                        periods.append(str(parsed_list))
                                except (ValueError, SyntaxError):
                                    # Try to extract period labels using regex
                                    period_pattern = (
                                        r"Period\([^)]*label=['\"]([^'\"]+)['\"][^)]*\)"
                                    )
                                    matches = re.findall(period_pattern, periods_data)
                                    periods.extend(matches)
                            else:
                                periods.append(periods_data)
                    elif isinstance(periods_data, list):
                        periods.extend(periods_data)
                    else:
                        periods.append(str(periods_data))
                except Exception as e:
                    log.debug(f"Could not parse periods data: {e}")

            # Check for nested job_info that might contain periods
            if "job_info" in processed_info:
                try:
                    nested_job_info = processed_info["job_info"]
                    if isinstance(nested_job_info, str):
                        nested_data = json.loads(nested_job_info)
                    elif isinstance(nested_job_info, dict):
                        nested_data = nested_job_info
                    else:
                        nested_data = {}

                    if isinstance(nested_data, dict):
                        # Extract periods from nested job_info
                        nested_periods = nested_data.get("periods", "")
                        if nested_periods:
                            try:
                                if isinstance(nested_periods, str):
                                    # Try to parse as JSON first
                                    try:
                                        parsed_nested_periods = json.loads(
                                            nested_periods
                                        )
                                        if isinstance(parsed_nested_periods, list):
                                            periods.extend(parsed_nested_periods)
                                        else:
                                            periods.append(str(parsed_nested_periods))
                                    except json.JSONDecodeError:
                                        # Try to extract period labels using regex
                                        import re

                                        period_pattern = r"Period\([^)]*label=['\"]([^'\"]+)['\"][^)]*\)"
                                        matches = re.findall(
                                            period_pattern, nested_periods
                                        )
                                        if matches:
                                            periods.extend(matches)
                                        else:
                                            periods.append(nested_periods)
                                elif isinstance(nested_periods, list):
                                    periods.extend(nested_periods)
                                else:
                                    periods.append(str(nested_periods))
                            except Exception as e:
                                log.debug(f"Could not parse nested periods data: {e}")

                        # Also check for info field that might contain periods
                        nested_info = nested_data.get("info", "")
                        if nested_info and isinstance(nested_info, str):
                            import re

                            period_pattern = (
                                r"Period\([^)]*label=['\"]([^'\"]+)['\"][^)]*\)"
                            )
                            matches = re.findall(period_pattern, nested_info)
                            periods.extend(matches)

                except (json.JSONDecodeError, TypeError, AttributeError):
                    log.debug("Could not parse nested job_info for periods")

            # Clean and deduplicate periods
            if periods:
                # Remove duplicates while preserving order
                seen = set()
                unique_periods = []
                for period in periods:
                    period_str = str(period).strip()
                    if (
                        period_str
                        and period_str not in seen
                        and period_str.lower() not in ["none", "null", ""]
                    ):
                        seen.add(period_str)
                        unique_periods.append(period_str)

                if unique_periods:
                    processed_info["periods"] = json.dumps(unique_periods)

            # Process facts/KPIs information from multiple sources
            facts = []

            # First, check for direct facts field
            facts_data = processed_info.get("facts", "")
            if facts_data:
                try:
                    if isinstance(facts_data, str):
                        # Try to parse as JSON first
                        try:
                            parsed_facts = json.loads(facts_data)
                            if isinstance(parsed_facts, list):
                                facts.extend(parsed_facts)
                            else:
                                facts.append(str(parsed_facts))
                        except json.JSONDecodeError:
                            # If not JSON, check if it's a Python list string
                            if facts_data.startswith("[") and facts_data.endswith("]"):
                                import ast

                                try:
                                    parsed_list = ast.literal_eval(facts_data)
                                    if isinstance(parsed_list, list):
                                        facts.extend(parsed_list)
                                    else:
                                        facts.append(str(parsed_list))
                                except (ValueError, SyntaxError):
                                    facts.append(facts_data)
                            else:
                                facts.append(facts_data)
                    elif isinstance(facts_data, list):
                        facts.extend(facts_data)
                    else:
                        facts.append(str(facts_data))
                except Exception as e:
                    log.debug(f"Could not parse facts data: {e}")

            # Also check for individual fact fields
            for key, value in processed_info.items():
                if ("fact" in key.lower() or "kpi" in key.lower()) and key != "facts":
                    if value and str(value).lower() not in ["none", "null", ""]:
                        facts.append(str(value))

            # Check for nested job_info that might contain facts
            if "job_info" in processed_info:
                try:
                    nested_job_info = processed_info["job_info"]
                    if isinstance(nested_job_info, str):
                        nested_data = json.loads(nested_job_info)
                    else:
                        nested_data = nested_job_info

                    if isinstance(nested_data, dict):
                        # Extract facts from nested job_info
                        nested_facts = nested_data.get("facts", [])
                        if isinstance(nested_facts, str):
                            try:
                                parsed_nested_facts = json.loads(nested_facts)
                                if isinstance(parsed_nested_facts, list):
                                    facts.extend(parsed_nested_facts)
                                else:
                                    facts.append(str(parsed_nested_facts))
                            except json.JSONDecodeError:
                                if nested_facts.startswith(
                                    "["
                                ) and nested_facts.endswith("]"):
                                    import ast

                                    try:
                                        parsed_list = ast.literal_eval(nested_facts)
                                        if isinstance(parsed_list, list):
                                            facts.extend(parsed_list)
                                        else:
                                            facts.append(str(parsed_list))
                                    except (ValueError, SyntaxError):
                                        facts.append(nested_facts)
                                else:
                                    facts.append(nested_facts)
                        elif isinstance(nested_facts, list):
                            facts.extend(nested_facts)

                        # Also check for nested_facts field
                        nested_facts_alt = nested_data.get("nested_facts", [])
                        if isinstance(nested_facts_alt, list):
                            facts.extend(nested_facts_alt)
                        elif nested_facts_alt:
                            facts.append(str(nested_facts_alt))

                        # Look for individual fact/KPI fields in nested data
                        for key, value in nested_data.items():
                            if (
                                ("fact" in key.lower() or "kpi" in key.lower())
                                and key not in ["facts", "nested_facts"]
                                and not isinstance(value, (dict, list))
                            ):
                                if value and str(value).lower() not in [
                                    "none",
                                    "null",
                                    "",
                                ]:
                                    facts.append(str(value))
                except (json.JSONDecodeError, TypeError, AttributeError):
                    log.debug("Could not parse nested job_info for facts")

            # Clean and deduplicate facts
            if facts:
                # Remove duplicates while preserving order
                seen = set()
                unique_facts = []
                for fact in facts:
                    fact_str = str(fact).strip()
                    if (
                        fact_str
                        and fact_str not in seen
                        and fact_str.lower() not in ["none", "null", ""]
                    ):
                        seen.add(fact_str)
                        unique_facts.append(fact_str)

                if unique_facts:
                    processed_info["facts"] = json.dumps(unique_facts)

        return processed_info

    def create_info_sheet_from_metadata(
        self, metadata: JobMetadata, filename: str, layout: Optional[ExcelLayout] = None
    ) -> None:
        """
        Create a comprehensive info sheet using JobMetadata structure.

        This method provides enhanced integration with the current export architecture
        by directly accepting JobMetadata objects and layout information.

        Args:
            metadata: JobMetadata object containing structured job information
            filename: The export filename
            layout: Optional Excel layout information for context
        """
        if not self.workbook:
            raise ValueError("Workbook is required for creating info sheets")

        try:
            log.debug(f"Creating info sheet from JobMetadata for job {metadata.job_id}")
            log.debug(
                f"JobMetadata details: table={metadata.table_name}, rows={metadata.total_rows}"
            )
            log.debug(
                f"Available periods: {len(metadata.available_periods) if metadata.available_periods else 0}"
            )
            log.debug(
                f"Facts list: {len(metadata.facts_list) if metadata.facts_list else 0}"
            )
            log.debug(
                f"Job info type: {type(metadata.job_info)}, available: {metadata.job_info is not None}"
            )

            # Convert JobMetadata to the format expected by create_info_sheet
            enhanced_job_info = {
                # Core metadata from JobMetadata
                "job_id": str(metadata.job_id),
                "table_name": metadata.table_name,
                "database_name": metadata.database_name,
                "total_rows": str(metadata.total_rows),
                "estimated_size_mb": str(metadata.estimated_size_mb),
                "created_at": metadata.created_at.isoformat()
                if metadata.created_at
                else "",
                # Structured data from JobMetadata
                "periods": json.dumps(metadata.available_periods)
                if metadata.available_periods
                else "",
                "facts": json.dumps(metadata.facts_list) if metadata.facts_list else "",
                # Layout information if provided
                "export_layout": layout.value if layout else "vertical",
                # Include the original job_info data
                **metadata.job_info,
            }

            log.debug(f"Enhanced job_info created with {len(enhanced_job_info)} fields")

            # Use the existing create_info_sheet method with enhanced data
            self.create_info_sheet(enhanced_job_info, filename)
            log.info(
                f"Info sheet created successfully from JobMetadata for job {metadata.job_id}"
            )

        except Exception as e:
            log.error(
                f"Failed to create info sheet from JobMetadata: {e}", exc_info=True
            )

    def generate_metadata_for_export(
        self, metadata: JobMetadata, filename: str, layout: Optional[ExcelLayout] = None
    ) -> Dict[str, str]:
        """
        Generate metadata dictionary for non-Excel export formats.

        This method creates a structured metadata dictionary that can be embedded
        in Parquet files or included as CSV headers/comments.

        Args:
            metadata: JobMetadata object containing structured job information
            filename: The export filename
            layout: Optional Excel layout information for context

        Returns:
            Dictionary of metadata suitable for embedding in export files
        """
        try:
            log.debug(f"Generating export metadata for job {metadata.job_id}")

            # Create enhanced job_info similar to create_info_sheet_from_metadata
            enhanced_job_info = {
                # Core metadata from JobMetadata
                "job_id": str(metadata.job_id),
                "table_name": metadata.table_name,
                "database_name": metadata.database_name,
                "total_rows": str(metadata.total_rows),
                "estimated_size_mb": str(metadata.estimated_size_mb),
                "created_at": metadata.created_at.isoformat()
                if metadata.created_at
                else "",
                # Structured data from JobMetadata
                "periods": json.dumps(metadata.available_periods)
                if metadata.available_periods
                else "",
                "facts": json.dumps(metadata.facts_list) if metadata.facts_list else "",
                # Layout information if provided
                "export_layout": layout.value if layout else "vertical",
                "export_filename": filename,
                # Include the original job_info data
                **metadata.job_info,
            }

            # Process the enhanced job_info to get clean, structured metadata
            processed_metadata = self.process_job_info(enhanced_job_info, filename)

            log.debug(f"Generated {len(processed_metadata)} metadata fields for export")
            return processed_metadata

        except Exception as e:
            log.warning(f"Failed to generate export metadata: {e}", exc_info=True)
            # Return basic metadata as fallback
            return {
                "job_id": str(metadata.job_id),
                "table_name": metadata.table_name,
                "total_rows": str(metadata.total_rows),
                "export_filename": filename,
            }

    def create_info_sheet(self, job_info: Any, filename: str) -> None:
        """
        Create a comprehensive info sheet with job information.

        Args:
            job_info: Job information to include in the sheet
            filename: The export filename
        """
        if not self.workbook:
            raise ValueError("Workbook is required for creating info sheets")

        try:
            log.debug(f"Creating info sheet with job_info type: {type(job_info)}")
            log.debug(f"Job info available: {job_info is not None}")
            if job_info and isinstance(job_info, dict):
                log.debug(
                    f"Job info has {len(job_info)} fields: {list(job_info.keys())}"
                )

            processed_info = self.process_job_info(job_info, filename)
            log.debug(f"Processed info has {len(processed_info)} fields")

            # Create Info sheet - this will be positioned as the last sheet
            info_sheet = self.workbook.add_worksheet("Info")

            # Write Result Information section
            info_sheet.write(0, 0, "Result Information", self.header_format)
            info_sheet.write(0, 1, "", self.header_format)

            row = 1

            # Key fields to display first (main job metadata)
            key_fields = [
                ("Result ID", "result_id"),
                ("Job ID", "job_id"),
                ("Analysis Name", "analysis_name"),
                ("KPI Type", "kpi_type"),
                ("Panel ID", "panel_id"),
                ("Created At", "created_at"),
                ("Job Duration", "job_duration"),
                ("Result Rows", "result_rows"),
                ("Username", "username"),
                ("Final Result Table", "final_result_table"),
            ]

            # Write key fields
            for display_name, field_key in key_fields:
                value = processed_info.get(field_key, "")
                if value:
                    info_sheet.write(row, 0, display_name)
                    info_sheet.write(row, 1, str(value))
                    row += 1

            row += 1  # Empty row

            # Write Axes section
            axes_data = processed_info.get("axes", "")
            if axes_data:
                try:
                    axes = (
                        json.loads(axes_data)
                        if isinstance(axes_data, str)
                        else axes_data
                    )
                    if isinstance(axes, dict) and axes:
                        info_sheet.write(row, 0, "Axes", self.section_header_format)
                        info_sheet.write(
                            row, 1, "Full Name", self.section_header_format
                        )
                        row += 1

                        for axis_key, axis_value in axes.items():
                            info_sheet.write(row, 0, str(axis_key))
                            info_sheet.write(row, 1, str(axis_value))
                            row += 1

                        row += 1  # Empty row
                except Exception as axes_error:
                    log.debug(f"Error processing axes data: {axes_error}")

            # Write Filters section
            filters_data = processed_info.get("filters", "")
            if filters_data:
                try:
                    filters = (
                        json.loads(filters_data)
                        if isinstance(filters_data, str)
                        else filters_data
                    )
                    if isinstance(filters, dict) and filters:
                        info_sheet.write(row, 0, "Filters", self.section_header_format)
                        info_sheet.write(row, 1, "Value", self.section_header_format)
                        row += 1

                        for filter_key, filter_value in filters.items():
                            info_sheet.write(row, 0, str(filter_key))
                            info_sheet.write(row, 1, str(filter_value))
                            row += 1

                        row += 1  # Empty row
                except Exception as filters_error:
                    log.debug(f"Error processing filters data: {filters_error}")

            # Write Periods section
            periods_data = processed_info.get("periods", "")
            if periods_data:
                try:
                    periods = (
                        json.loads(periods_data)
                        if isinstance(periods_data, str)
                        else periods_data
                    )
                    if isinstance(periods, list) and periods:
                        info_sheet.write(row, 0, "Periods", self.section_header_format)
                        info_sheet.write(row, 1, "Value", self.section_header_format)
                        row += 1

                        for period in periods:
                            if period and str(period).strip():
                                info_sheet.write(row, 0, "Period")
                                info_sheet.write(row, 1, str(period).strip())
                                row += 1

                        row += 1  # Empty row
                except Exception as periods_error:
                    log.debug(f"Error processing periods data: {periods_error}")

            # Write Facts/KPIs section
            facts_data = processed_info.get("facts", "")
            if facts_data:
                try:
                    facts = (
                        json.loads(facts_data)
                        if isinstance(facts_data, str)
                        else facts_data
                    )
                    if isinstance(facts, list) and facts:
                        info_sheet.write(
                            row, 0, "Facts/KPIs", self.section_header_format
                        )
                        info_sheet.write(row, 1, "Value", self.section_header_format)
                        row += 1

                        for fact in facts:
                            if fact and str(fact).strip():
                                info_sheet.write(row, 0, "Fact")
                                info_sheet.write(row, 1, str(fact).strip())
                                row += 1

                        row += 1  # Empty row
                except Exception as facts_error:
                    log.debug(f"Error processing facts data: {facts_error}")

            # Write any additional fields not covered above
            excluded_fields = {
                "result_id",
                "job_id",
                "analysis_name",
                "kpi_type",
                "panel_id",
                "created_at",
                "job_duration",
                "result_rows",
                "username",
                "final_result_table",
                "axes",
                "filters",
                "periods",  # Exclude periods (already processed)
                "facts",  # Exclude facts (already processed)
                "id_panel",
                "job_info",  # Exclude raw job_info dictionary
                "parameters",  # Exclude raw parameters
                "nested_axes",  # Exclude nested axes (already processed)
                "nested_filters",  # Exclude nested filters (already processed)
                "nested_periods",  # Exclude nested periods (already processed)
                "nested_facts",  # Exclude nested facts (already processed)
                "raw_json_field",  # Exclude obvious raw JSON fields
                "raw_data",  # Exclude raw data fields
            }

            # Filter out additional raw JSON fields and empty values
            other_fields = {}
            for k, v in processed_info.items():
                if k not in excluded_fields and v:
                    # Skip fields that look like raw JSON dumps
                    str_value = str(v).strip()
                    if (str_value.startswith("{") and str_value.endswith("}")) or (
                        str_value.startswith("[") and str_value.endswith("]")
                    ):
                        # Skip raw JSON strings unless they're short and meaningful
                        if len(str_value) > 50:  # More restrictive threshold
                            continue

                    # Skip fields with axis_, filter_, period_, fact_, or kpi_ prefixes (already processed)
                    if (
                        k.startswith("axis_")
                        or k.endswith("_axis")
                        or "filter" in k.lower()
                        or "period" in k.lower()
                        or "fact" in k.lower()
                        or "kpi" in k.lower()
                    ):
                        continue

                    # Skip empty or meaningless values
                    if str_value.lower() in ["none", "null", "{}", "[]", "0", ""]:
                        continue

                    other_fields[k] = v

            if other_fields:
                info_sheet.write(
                    row, 0, "Additional Information", self.section_header_format
                )
                info_sheet.write(row, 1, "Value", self.section_header_format)
                row += 1

                for key, value in other_fields.items():
                    display_key = str(key).replace("_", " ").title()
                    display_value = str(value)

                    # Handle list-like strings
                    if display_value.startswith("[") and display_value.endswith("]"):
                        try:
                            import ast

                            parsed_list = ast.literal_eval(display_value)
                            if isinstance(parsed_list, list):
                                display_value = ", ".join(
                                    str(item) for item in parsed_list
                                )
                        except (ValueError, SyntaxError):
                            pass  # Keep original value

                    info_sheet.write(row, 0, display_key)
                    info_sheet.write(row, 1, display_value)
                    row += 1

            # Set column widths for better readability
            info_sheet.set_column(0, 0, 25)  # Field names column
            info_sheet.set_column(1, 1, 50)  # Values column

            log.debug(f"Info sheet created with {row} rows of information")
            log.info(f"Info sheet creation completed successfully for {filename}")

        except Exception as e:
            log.error(f"Failed to create info sheet: {e}", exc_info=True)
