"""
Startup integration for export monitoring.

This module provides initialization and integration of export monitoring
with the existing MagicGateway monitoring infrastructure.
"""

from magic_gateway.core.logging_config import log
from magic_gateway.export.monitoring import (
    export_performance_tracker,
    export_monitoring_integration
)


async def initialize_export_monitoring() -> None:
    """
    Initialize export monitoring integration.
    
    This function sets up the export monitoring system and integrates it
    with the existing monitoring infrastructure.
    """
    try:
        # Import the existing monitoring service
        from magic_gateway.monitoring.service import connection_pool_monitoring_service
        
        # Set up the integration with connection pool monitoring
        export_monitoring_integration.connection_pool_monitor = connection_pool_monitoring_service
        
        log.info("Export monitoring integration initialized successfully")
        
        # Log initial status
        summary = export_performance_tracker.get_performance_summary()
        log.info(
            f"Export performance tracker initialized: "
            f"active_operations={summary['active_operations']}, "
            f"completed_operations={summary['completed_operations']}"
        )
        
    except Exception as e:
        log.error(f"Failed to initialize export monitoring integration: {e}", exc_info=True)
        # Don't raise the exception to avoid breaking application startup
        # Export monitoring is not critical for basic application functionality


async def cleanup_export_monitoring() -> None:
    """
    Cleanup export monitoring resources.
    
    This function should be called during application shutdown to properly
    clean up export monitoring resources.
    """
    try:
        # Log final statistics
        summary = export_performance_tracker.get_performance_summary()
        log.info(
            f"Export monitoring shutdown: "
            f"final_active_operations={summary['active_operations']}, "
            f"total_completed_operations={summary['completed_operations']}, "
            f"success_rate={summary.get('success_rate', 0):.2%}"
        )
        
        # Any additional cleanup can be added here
        
    except Exception as e:
        log.error(f"Error during export monitoring cleanup: {e}", exc_info=True)