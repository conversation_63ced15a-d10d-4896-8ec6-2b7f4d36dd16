"""API models for the MagicGateway application."""

import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List, Union, Literal

from pydantic import BaseModel, Field


# Authentication models
class LoginRequest(BaseModel):
    """Model for a login request."""

    username: str = Field(
        ...,
        description="LDAP username (UPN format for users, DN format for service accounts)",
    )
    password: str = Field(..., description="LDAP password")


class Token(BaseModel):
    """Model for an authentication token."""

    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int


class RefreshTokenRequest(BaseModel):
    """Model for a refresh token request."""

    refresh_token: str = Field(
        ..., description="The refresh token to use for generating a new access token"
    )


class TokenPayload(BaseModel):
    """Model for a token payload."""

    sub: Optional[str] = None
    exp: Optional[int] = None
    iat: Optional[int] = None
    type: Optional[str] = None
    user_id: Optional[int] = None
    is_admin: Optional[bool] = None
    auth_source: Optional[str] = None


class User(BaseModel):
    """Model for a user."""

    username: str
    is_admin: bool = False
    auth_source: str = "ldap"


# Script models
class ScriptParameter(BaseModel):
    """Model for a script parameter."""

    name: str
    type: str
    required: bool
    default: Optional[Any] = None
    description: Optional[str] = None


class ScriptMetadata(BaseModel):
    """Model for script metadata."""

    name: str
    description: str
    author: Optional[str] = None
    version: Optional[str] = None
    requires_admin: bool = False


class ScriptInfo(BaseModel):
    """Model for script information."""

    name: str
    metadata: ScriptMetadata
    parameters: List[ScriptParameter]


class ScriptRequest(BaseModel):
    """Model for a script execution request."""

    parameters: Dict[str, Any] = {}
    script_id: Optional[str] = None


class ScriptResponse(BaseModel):
    """Model for a script execution response."""

    script_id: str
    script_name: str
    start_time: datetime
    end_time: datetime
    execution_time: float
    result: Any
    error: Optional[str] = None


# Health check models
class DatabaseStatus(BaseModel):
    """Model for database connection status."""

    connected: bool
    message: Optional[str] = None
    last_checked: datetime


class ConnectionPoolStatus(BaseModel):
    """Model for connection pool status."""

    pool_type: str
    active_connections: int
    idle_connections: int
    min_size: int
    max_size: int
    utilization_percent: float
    status: str  # 'healthy', 'warning', 'critical'
    last_updated: datetime
    acquisition_time_ms: Optional[float] = None
    usage_time_ms: Optional[float] = None
    timeouts: Optional[int] = None
    errors: Optional[int] = None


class HealthCheck(BaseModel):
    """Model for a health check response."""

    status: str
    version: str
    timestamp: datetime
    databases: Dict[str, DatabaseStatus]
    connection_pools: Dict[str, ConnectionPoolStatus]
    uptime: float
    export_performance: Optional[Dict[str, Any]] = None


# Error models
class ErrorResponse(BaseModel):
    """Model for an error response."""

    detail: str


# PostgreSQL to ClickHouse conversion models
class PgToClickHouseConversionRequest(BaseModel):
    """Model for a PostgreSQL to ClickHouse conversion request."""

    object_name: str = Field(
        ..., description="Full object name including schema (e.g., 'schema.table_name')"
    )
    output_mode: Optional[str] = Field(
        "combined",
        description="Output mode: 'normal' for a single statement, 'combined' for WITH clause, 'cte_only' for CTE part, 'queries_only' for queries part, 'queries_split' for list of queries split by UNION ALL, 'click_app' for list with CTE and queries as dictionary",
    )
    id_panel: Optional[str] = Field(
        None,
        description="Panel ID to use for table mapping (e.g., '5' or '8'). If not provided, will be used main panel if applicable.",
    )
    timeout_seconds: Optional[int] = Field(
        None,
        description="Custom timeout in seconds for this request. If not provided, the default MAX_QUERY_EXECUTION_TIME will be used.",
    )


class PgToClickHouseConversionResponse(BaseModel):
    """Model for a PostgreSQL to ClickHouse conversion response."""

    request_id: uuid.UUID
    original_ddl: str
    clickhouse_sql: Union[str, List[str], Dict[str, Any]] = Field(
        ...,
        description="ClickHouse SQL result. String for most output modes, list of strings for 'queries_split' mode, dictionary for 'click_app' mode",
    )
    output_mode: str
    status: str = "completed"


# PostgreSQL to ClickHouse view checker models
class PgToChViewCheckerRequest(BaseModel):
    """Model for a PostgreSQL to ClickHouse view checker request."""

    pg_view_name: str = Field(
        ...,
        description="Full name of PostgreSQL view including schema (e.g., 'schema.view_name')",
    )
    id_panel: Optional[str] = Field(
        None,
        description="Panel ID to use for table mapping (e.g., '5' or '8'). If not provided, will be used main panel.",
    )

    timeout_seconds: Optional[int] = Field(
        None,
        description="Custom timeout in seconds for this request. If not provided, the default MAX_QUERY_EXECUTION_TIME will be used.",
    )


class PgToChViewCheckerResponse(BaseModel):
    """Model for a PostgreSQL to ClickHouse view checker response."""

    request_id: uuid.UUID
    pg_view_name: str
    pg_row_count: int
    ch_row_count: int
    id_panel: int
    row_count_match: bool
    status: str
    error: Optional[str] = None


# Assortment Optimizer models
class AssortmentOptimizerParams(BaseModel):
    """
    Model for assortment optimizer parameters.

    This model defines all the parameters required for running the assortment optimization script.
    The optimization analyzes product assortment data within a specified date range and generates
    recommendations based on the provided configuration.
    """

    start_date: str = Field(
        ...,
        pattern=r"^\d{4}-\d{2}-\d{2}$",
        description="Start date for the analysis period in YYYY-MM-DD format. Must be before or equal to end_date.",
        example="2024-01-01",
        title="Analysis Start Date",
    )
    end_date: str = Field(
        ...,
        pattern=r"^\d{4}-\d{2}-\d{2}$",
        description="End date for the analysis period in YYYY-MM-DD format. Must be after or equal to start_date.",
        example="2024-12-31",
        title="Analysis End Date",
    )
    json_axis_id: int = Field(
        ...,
        ge=1,
        description="JSON axis identifier used for data filtering and analysis. Must be a positive integer corresponding to a valid axis in the system.",
        example=123,
        title="JSON Axis ID",
    )
    json_flt_hh_id: Optional[int] = Field(
        None,
        ge=1,
        description="Optional JSON filter household ID for additional data filtering. If provided, must be a positive integer. Leave null/empty if no household filtering is required.",
        example=456,
        title="JSON Filter Household ID",
    )
    total_position_number: int = Field(
        ...,
        ge=1,
        description="Total number of positions to consider in the optimization analysis. Must be a positive integer representing the maximum number of product positions.",
        example=10,
        title="Total Position Number",
    )
    rest_position_number: int = Field(
        ...,
        ge=-1,
        description="Number of REST positions in the analysis. Use -1 to indicate no REST positions in the axis, or a non-negative integer for the specific number of REST positions.",
        example=5,
        title="REST Position Number",
    )
    id_panel: int = Field(
        ...,
        ge=1,
        description="Panel identifier specifying which data panel to use for the analysis. Must be a positive integer corresponding to a valid panel in the system.",
        example=8,
        title="Panel ID",
    )

    class Config:
        """Pydantic model configuration."""

        json_schema_extra = {
            "example": {
                "start_date": "2024-01-01",
                "end_date": "2024-12-31",
                "json_axis_id": 123,
                "json_flt_hh_id": 456,
                "total_position_number": 10,
                "rest_position_number": 5,
                "id_panel": 8,
            }
        }


class AssortmentOptimizerResponse(ScriptResponse):
    """
    Enhanced script response for assortment optimizer with file download support.

    This response model extends the base ScriptResponse with additional fields specific
    to the assortment optimization script, including file download capabilities.
    """

    result_file_id: Optional[str] = Field(
        None,
        description="Unique identifier for the generated Excel result file. Use this ID with the download endpoint to retrieve the optimization results.",
        example="ao_12345678-1234-1234-1234-123456789abc",
        title="Result File ID",
    )
    download_url: Optional[str] = Field(
        None,
        description="Complete URL endpoint for downloading the result file. This URL can be used directly to download the Excel file containing the optimization results.",
        example="/api/v1/scripts/download/ao_12345678-1234-1234-1234-123456789abc",
        title="Download URL",
    )

    class Config:
        """Pydantic model configuration."""

        json_schema_extra = {
            "example": {
                "script_id": "assortment_optimizer_20240101_123456",
                "script_name": "assortment_optimizer",
                "start_time": "2024-01-01T10:00:00Z",
                "end_time": "2024-01-01T10:05:30Z",
                "execution_time": 330.5,
                "result": {
                    "status": "success",
                    "message": "Assortment optimization completed successfully",
                    "execution_time_seconds": 330.5,
                    "parameters": {
                        "start_date": "2024-01-01",
                        "end_date": "2024-12-31",
                        "json_axis_id": 123,
                        "json_flt_hh_id": 456,
                        "total_position_number": 10,
                        "rest_position_number": 5,
                        "id_panel": 8,
                    },
                },
                "error": None,
                "result_file_id": "ao_12345678-1234-1234-1234-123456789abc",
                "download_url": "/api/v1/scripts/download/ao_12345678-1234-1234-1234-123456789abc",
            }
        }


# Data export models (for scripts endpoint)
class ExportFormat(str, Enum):
    """Enum for export formats with enhanced layout support."""

    CSV = "csv"
    EXCEL = "excel"
    PARQUET = "parquet"


class ExcelLayout(str, Enum):
    """Excel layout options for data presentation."""

    VERTICAL = "vertical"  # Data as stored in database
    HORIZONTAL = "horizontal"  # Facts as columns


class PeriodSeparation(str, Enum):
    """Period separation options for exports."""

    NONE = "none"  # All periods in single file/sheet
    SHEETS = "sheets"  # Separate sheets (Excel only)
    FILES = "files"  # Separate files in ZIP archive


class ExportRequest(BaseModel):
    """Model for export request parameters."""

    format: ExportFormat = Field(
        ExportFormat.EXCEL, description="Export format (csv, excel, parquet)"
    )
    horizontal_facts: bool = Field(
        True,
        description="Use horizontal facts layout (facts as columns) for all export formats. When true, automatically uses pivot queries for data transformation.",
    )
    separate_periods: PeriodSeparation = Field(
        PeriodSeparation.NONE,
        description="Period separation mode: 'none' for single file/sheet, 'sheets' for separate Excel sheets, 'files' for separate files in ZIP archive.",
    )
    compression: bool = Field(
        True, description="Enable compression for supported formats (ZIP for CSV)"
    )
    timeout_seconds: Optional[int] = Field(
        None, ge=1, le=3600, description="Custom timeout in seconds (1-3600 seconds)"
    )
    use_download_url: bool = Field(
        False,
        description="Return download URL instead of streaming file directly (recommended for large files accessed via OpenAPI docs)",
    )

    def model_post_init(self, __context) -> None:
        """Validate parameter combinations after model initialization."""
        # Validate period separation compatibility with format
        if (
            self.separate_periods == PeriodSeparation.SHEETS
            and self.format != ExportFormat.EXCEL
        ):
            raise ValueError(
                f"Period separation by sheets is only supported for Excel format. "
                f"For {self.format.value} format, use 'files' or 'none' instead."
            )


class ExportResponseDetails(BaseModel):
    """Model for export response details."""

    file_id: Optional[str] = None
    filename: Optional[str] = None
    file_size: Optional[int] = None
    content_type: Optional[str] = None
    download_url: Optional[str] = None
    processing_time_seconds: Optional[float] = None
    total_rows: Optional[int] = None
    excel_limit: Optional[int] = None


class ExportResponse(BaseModel):
    """Model for unified export response."""

    job_id: int
    format: ExportFormat
    request_id: str
    status: str = "completed"
    message: Optional[str] = None
    details: Optional[ExportResponseDetails] = None
    instructions: Optional[List[str]] = None


class ExportErrorResponse(BaseModel):
    """Model for unified export error response."""

    error: str
    message: str
    request_id: str
    details: Optional[Dict[str, Any]] = None
    instructions: Optional[List[str]] = None
