"""
Optimized Export Handler for job results export.

This module provides the main orchestration class for coordinating export operations,
including job metadata retrieval, connection pool selection, and export workflow management.
"""

import uuid
from datetime import datetime, timezone
from typing import Dict, Any, Optional

from fastapi import HTTPException
from fastapi.responses import StreamingResponse

from magic_gateway.core.logging_config import log
from magic_gateway.db.clickhouse_handler import ClickHouseHandler
from magic_gateway.export.adapters import ConnectionPoolAdapter
from magic_gateway.export.exceptions import (
    ExportError,
    TableNotFoundError,
)
from magic_gateway.export.models import (
    ExportContext,
    ExportFormat,
    ExportOptions,
)
from magic_gateway.export.monitoring import (
    export_performance_tracker,
    export_monitoring_integration
)


class OptimizedExportHandler:
    """
    Main orchestration class for optimized job export operations.

    This class coordinates the entire export process from job metadata retrieval
    through connection pool selection to final response generation.

    Features:
    - Job metadata retrieval and validation
    - Connection pool selection via ConnectionPoolAdapter
    - Basic export workflow coordination
    - Comprehensive error handling and logging
    """

    def __init__(self, connection_adapter: Optional[ConnectionPoolAdapter] = None):
        """
        Initialize the OptimizedExportHandler.

        Args:
            connection_adapter: Optional ConnectionPoolAdapter instance.
                              If None, creates a new instance with default registry.
        """
        self.connection_adapter = connection_adapter or ConnectionPoolAdapter()
        self.request_id = str(uuid.uuid4())

    async def export_job_data(
        self,
        job_id: int,
        format: ExportFormat,
        separate_periods: bool = False,
        horizontal_facts: bool = False,
        request_id: Optional[str] = None,
    ) -> StreamingResponse:
        """
        Export job data in the specified format.

        This is the main entry point for export operations. It orchestrates
        the entire export process from metadata retrieval to response generation.

        Args:
            job_id: ID of the job to export
            format: Target export format
            separate_periods: Whether to separate periods in Excel exports
            horizontal_facts: Whether to use horizontal facts layout
            request_id: Optional request ID for tracking

        Returns:
            StreamingResponse containing the exported data

        Raises:
            ExportError: If export operation fails
            TableNotFoundError: If job result table is not found
            ConnectionPoolExhaustedError: If no connections are available
        """
        # Use provided request_id or generate new one
        if request_id:
            self.request_id = request_id
        else:
            self.request_id = str(uuid.uuid4())

        log.info(
            f"Starting export for job {job_id} in format {format.value} (request: {self.request_id})"
        )

        try:
            # Step 1: Retrieve and validate job metadata
            job_metadata = await self._retrieve_job_metadata(job_id)

            # Step 1.5: Record the export access (matching original system)
            await self._record_export_access(job_id, format, self.request_id)

            # Step 2: Create export context
            export_context = await self._create_export_context(
                job_id=job_id,
                format=format,
                separate_periods=separate_periods,
                horizontal_facts=horizontal_facts,
                metadata=job_metadata,
            )

            # Step 3: Select optimal connection pool
            connection_selection = await self.connection_adapter.get_optimal_connection(
                table_name=export_context.table_name, request_id=self.request_id
            )

            # Update context with selected connection
            export_context.connection_manager = connection_selection.manager

            log.info(
                f"Selected connection pool: {connection_selection.routing_reason} "
                f"(cluster: {connection_selection.cluster_name}, fallback: {connection_selection.is_fallback}) "
                f"for job {job_id} (request: {self.request_id})"
            )

            # Step 4: Coordinate export with performance tracking
            async with export_performance_tracker.track_operation(
                export_context, "complete_export"
            ) as metrics:
                try:
                    response = await self._coordinate_export(export_context)
                    
                    # Log successful export metrics
                    await export_monitoring_integration.log_export_metrics(metrics)
                    
                    return response
                    
                except Exception as e:
                    # Log export error with detailed context
                    await export_monitoring_integration.log_export_error(
                        job_id=job_id,
                        request_id=self.request_id,
                        error_type=type(e).__name__,
                        error_message=str(e),
                        error_context={
                            "format": format.value,
                            "table_name": export_context.table_name,
                            "connection_pool": type(export_context.connection_manager).__name__ if export_context.connection_manager else None,
                            "separate_periods": separate_periods,
                            "horizontal_facts": horizontal_facts
                        },
                        operation_stage="complete_export",
                        recovery_suggestions=getattr(e, 'recovery_suggestions', [])
                    )
                    raise

        except Exception as e:
            log.error(
                f"Export failed for job {job_id} (request: {self.request_id}): {str(e)}"
            )
            if isinstance(e, ExportError):
                raise
            elif isinstance(e, HTTPException):
                # Re-raise HTTP exceptions as-is (don't wrap them)
                raise
            else:
                # Wrap unexpected errors in ExportError
                raise ExportError(
                    error_type="unexpected_error",
                    message=f"Unexpected error during export: {str(e)}",
                    context={
                        "job_id": job_id,
                        "format": format.value,
                        "request_id": self.request_id,
                    },
                    request_id=self.request_id,
                    recovery_suggestions=[
                        "Check job ID validity",
                        "Verify database connectivity",
                        "Review system logs for detailed error information",
                    ],
                )

    async def _retrieve_job_metadata(self, job_id: int) -> Dict[str, Any]:
        """
        Retrieve and validate job metadata from the database.

        Args:
            job_id: ID of the job to retrieve metadata for

        Returns:
            Dictionary containing job metadata

        Raises:
            TableNotFoundError: If job result table is not found
            ExportError: If metadata retrieval fails
        """
        log.debug(f"Retrieving metadata for job {job_id} (request: {self.request_id})")

        try:
            # Query metadata.results_metadata table to get the actual table name
            # This matches the approach used by the original export system
            metadata_query = f"""
                SELECT final_result_table, analysis_name, job_info
                FROM metadata.results_metadata
                WHERE job_id = '{job_id}'
                ORDER BY created_at DESC
                LIMIT 1
            """

            # Execute metadata query
            metadata_rows, _ = await ClickHouseHandler.execute_query(
                query=metadata_query,
                query_id=f"metadata_check_{self.request_id}",
                allow_write=False,
            )

            if not metadata_rows:
                raise TableNotFoundError(
                    request_id=self.request_id,
                    job_id=job_id,
                    table_name=f"job_{job_id}",
                    database_name="metadata",
                    context={
                        "query": "metadata.results_metadata",
                        "request_id": self.request_id,
                    },
                )

            # Extract metadata from the results_metadata table
            raw_metadata = metadata_rows[0]
            final_result_table = raw_metadata.get("final_result_table")
            analysis_name = raw_metadata.get("analysis_name", f"job_{job_id}")
            job_info = raw_metadata.get("job_info", {})

            if not final_result_table:
                raise TableNotFoundError(
                    request_id=self.request_id,
                    job_id=job_id,
                    table_name=f"job_{job_id}",
                    database_name="metadata",
                    context={
                        "error": "final_result_table is empty",
                        "request_id": self.request_id,
                    },
                )

            # Process job_info if it's a string (JSON)
            if isinstance(job_info, str):
                try:
                    import json

                    parsed_job_info = json.loads(job_info)
                    if isinstance(parsed_job_info, dict):
                        job_info = {**raw_metadata, **parsed_job_info}
                    else:
                        job_info = {**raw_metadata, "job_info": job_info}
                except Exception as e:
                    log.warning(f"Failed to parse job_info as JSON: {e}")
                    job_info = {**raw_metadata, "job_info": job_info}
            else:
                job_info = {
                    **raw_metadata,
                    **(job_info if isinstance(job_info, dict) else {}),
                }

            # Extract database name from final_result_table
            database_name = (
                final_result_table.split(".")[0]
                if "." in final_result_table
                else "unknown"
            )

            metadata = {
                "table_name": final_result_table,
                "database_name": database_name,
                "analysis_name": analysis_name,
                "job_info": job_info,
                "job_id": job_id,
            }

            log.debug(
                f"Retrieved metadata for job {job_id}: "
                f"table={final_result_table}, analysis={analysis_name} "
                f"(request: {self.request_id})"
            )

            return metadata

        except TableNotFoundError:
            # Re-raise table not found errors as-is
            raise
        except Exception as e:
            log.error(
                f"Failed to retrieve metadata for job {job_id} (request: {self.request_id}): {str(e)}"
            )
            raise ExportError(
                error_type="metadata_retrieval_failed",
                message=f"Failed to retrieve job metadata: {str(e)}",
                context={"job_id": job_id, "request_id": self.request_id},
                request_id=self.request_id,
                recovery_suggestions=[
                    "Check database connectivity",
                    "Verify job ID is valid",
                    "Review database permissions",
                ],
            )

    async def _create_export_context(
        self,
        job_id: int,
        format: ExportFormat,
        separate_periods: bool,
        horizontal_facts: bool,
        metadata: Dict[str, Any],
    ) -> ExportContext:
        """
        Create an ExportContext object from job metadata and export parameters.

        Args:
            job_id: ID of the job being exported
            format: Target export format
            separate_periods: Whether to separate periods in Excel exports
            horizontal_facts: Whether to use horizontal facts layout
            metadata: Job metadata dictionary

        Returns:
            ExportContext object ready for export operations
        """
        export_options = ExportOptions(
            format=format,
            separate_periods=separate_periods,
            horizontal_facts=horizontal_facts,  # Allow horizontal_facts for all formats
            timeout_seconds=None,  # Use default timeout
        )

        # Convert request_id to UUID if it's a string, otherwise generate a new one
        try:
            request_uuid = (
                uuid.UUID(self.request_id)
                if len(self.request_id) == 36 and "-" in self.request_id
                else uuid.uuid4()
            )
        except ValueError:
            request_uuid = uuid.uuid4()

        context = ExportContext(
            job_id=job_id,
            request_id=request_uuid,
            table_name=metadata["table_name"],
            database_name=metadata["database_name"],
            format=format,
            options=export_options,
            metadata=None,  # Will be populated with JobMetadata in future tasks
            connection_manager=None,  # Will be set after connection selection
        )

        log.debug(
            f"Created export context for job {job_id} (request: {self.request_id})"
        )
        return context

    async def _coordinate_export(
        self, export_context: ExportContext
    ) -> StreamingResponse:
        """
        Coordinate the unified parquet export workflow.

        This method integrates the ConnectionPoolAdapter with the FormatConversionPipeline
        to provide streaming response generation with comprehensive error handling and
        guaranteed resource cleanup via ExportResourceManager.

        Args:
            export_context: Export context containing all necessary information

        Returns:
            StreamingResponse containing the exported data

        Raises:
            ExportError: If export coordination fails
        """
        log.info(
            f"Coordinating unified parquet export for job {export_context.job_id} "
            f"in format {export_context.format.value} "
            f"(request: {export_context.request_id})"
        )

        try:
            # Import required components
            from magic_gateway.export.pipeline.format_conversion import (
                FormatConversionPipeline,
            )
            from magic_gateway.export.resources.manager import ExportResourceManager
            from magic_gateway.export.models import ConversionOptions

            # Create resource manager for guaranteed cleanup
            async with ExportResourceManager() as resource_manager:
                log.debug(
                    f"Initialized ExportResourceManager for job {export_context.job_id} "
                    f"(resource_manager_id: {resource_manager.request_id}, "
                    f"export_request_id: {export_context.request_id})"
                )

                # Step 1: Build the export query
                export_query = await self._build_export_query(export_context)
                query_id = f"export_{export_context.job_id}_{export_context.request_id}"

                log.debug(
                    f"Built export query for job {export_context.job_id}: "
                    f"{export_query[:100]}... (request: {export_context.request_id})"
                )

                # Step 2: Log connection pool status before streaming
                await self._log_connection_pool_status(
                    export_context, "before_streaming"
                )

                # Step 3: Stream parquet data from connection pool
                try:
                    parquet_stream = (
                        self.connection_adapter.export_to_parquet_stream_with_fallback(
                            query=export_query,
                            table_name=export_context.table_name,
                            query_id=query_id,
                            request_id=str(export_context.request_id),
                            max_retries=2,
                        )
                    )

                    # Register the parquet stream for cleanup
                    stream_resource_id = resource_manager.register_stream(
                        stream=parquet_stream,
                        resource_id=f"parquet_stream_{export_context.job_id}",
                        cleanup_callback=self._cleanup_parquet_stream,
                    )

                    log.debug(
                        f"Started parquet streaming for job {export_context.job_id} "
                        f"(stream_resource_id: {stream_resource_id})"
                    )

                except Exception as e:
                    log.error(
                        f"Failed to start parquet streaming for job {export_context.job_id}: {e}"
                    )
                    await self._log_connection_pool_status(
                        export_context, "streaming_failed"
                    )
                    raise ExportError(
                        error_type="parquet_streaming_failed",
                        message=f"Failed to start parquet data streaming: {str(e)}",
                        context={
                            "job_id": export_context.job_id,
                            "table_name": export_context.table_name,
                            "query_id": query_id,
                            "error": str(e),
                            "connection_manager": str(
                                export_context.connection_manager
                            ),
                        },
                        request_id=str(export_context.request_id),
                        recovery_suggestions=[
                            "Check ClickHouse server connectivity",
                            "Verify table exists and is accessible",
                            "Check connection pool health",
                            "Retry the export request",
                        ],
                    ) from e

                # Step 4: Create format conversion pipeline with resource manager
                conversion_pipeline = FormatConversionPipeline(
                    resource_manager=resource_manager
                )

                # Step 5: Prepare conversion metadata and options
                conversion_metadata = await self._prepare_conversion_metadata(
                    export_context
                )
                conversion_options = ConversionOptions(
                    separate_periods=export_context.options.separate_periods,
                    horizontal_facts=export_context.options.horizontal_facts,
                    excel_layout=export_context.options.excel_layout,
                    compression=export_context.options.compression,
                    metadata=None,  # Will be populated with JobMetadata in future tasks
                )

                log.debug(
                    f"Prepared conversion options for job {export_context.job_id}: "
                    f"separate_periods={conversion_options.separate_periods}, "
                    f"horizontal_facts={conversion_options.horizontal_facts}, "
                    f"excel_layout={conversion_options.excel_layout}"
                )

                # Step 6: Convert and stream the response
                try:
                    streaming_response = await conversion_pipeline.convert_stream(
                        parquet_stream=parquet_stream,
                        target_format=export_context.format,
                        metadata=conversion_metadata,
                        options=conversion_options,
                        connection_manager=export_context.connection_manager,
                    )

                    # Log successful conversion and resource status
                    resource_summary = resource_manager.list_resources()
                    log.info(
                        f"Successfully created streaming response for job {export_context.job_id} "
                        f"in format {export_context.format.value} "
                        f"(request: {export_context.request_id}, "
                        f"resources_managed: {len(resource_summary)})"
                    )

                    # Log detailed resource information at debug level
                    if resource_summary:
                        log.debug(
                            f"Resource summary for job {export_context.job_id}: "
                            f"{resource_summary}"
                        )

                    await self._log_connection_pool_status(
                        export_context, "conversion_complete"
                    )

                    return streaming_response

                except Exception as e:
                    log.error(
                        f"Format conversion failed for job {export_context.job_id}: {e}"
                    )
                    await self._log_connection_pool_status(
                        export_context, "conversion_failed"
                    )

                    # Log resource status on failure
                    resource_summary = resource_manager.list_resources()
                    log.debug(
                        f"Resource status on conversion failure for job {export_context.job_id}: "
                        f"{resource_summary}"
                    )

                    raise ExportError(
                        error_type="format_conversion_failed",
                        message=f"Failed to convert parquet data to {export_context.format.value}: {str(e)}",
                        context={
                            "job_id": export_context.job_id,
                            "target_format": export_context.format.value,
                            "query_id": query_id,
                            "error": str(e),
                            "resources_managed": len(resource_summary),
                        },
                        request_id=str(export_context.request_id),
                        recovery_suggestions=[
                            "Try a different export format",
                            "Check data compatibility with target format",
                            "Verify export options are valid",
                            "Retry the export request",
                        ],
                    ) from e

        except ExportError:
            # Re-raise ExportError exceptions as-is
            raise
        except HTTPException:
            # Re-raise HTTP exceptions as-is
            raise
        except Exception as e:
            log.error(
                f"Unexpected error during export coordination for job {export_context.job_id}: {e}",
                exc_info=True,
            )
            raise ExportError(
                error_type="export_coordination_failed",
                message=f"Unexpected error during export coordination: {str(e)}",
                context={
                    "job_id": export_context.job_id,
                    "format": export_context.format.value,
                    "table_name": export_context.table_name,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
                request_id=str(export_context.request_id),
                recovery_suggestions=[
                    "Check system resources and connectivity",
                    "Verify export parameters are valid",
                    "Review system logs for detailed error information",
                    "Contact system administrator if the issue persists",
                ],
            ) from e

    async def _build_export_query(self, export_context: ExportContext) -> str:
        """
        Build the SQL query for exporting job data.

        Args:
            export_context: Export context containing job and table information

        Returns:
            SQL query string for data export
        """
        # Build a simple SELECT * query for the job result table
        # The actual table existence and data validation will happen during query execution
        # If the table doesn't exist or has issues, the query execution will fail with appropriate errors
        query = f"SELECT * FROM {export_context.table_name}"

        log.debug(
            f"Built export query for job {export_context.job_id}: {query} "
            f"(request: {export_context.request_id})"
        )

        return query

    async def _prepare_conversion_metadata(
        self, export_context: ExportContext
    ) -> Dict[str, Any]:
        """
        Prepare metadata for the format conversion pipeline.

        Args:
            export_context: Export context containing job and export information

        Returns:
            Dictionary containing metadata for format conversion
        """
        # Generate filename based on job ID
        analysis_name = f"job_{export_context.job_id}"
        sanitized_name = "".join(
            c if c.isalnum() or c in "_- " else "_" for c in analysis_name
        )
        filename = f"{sanitized_name}_job{export_context.job_id}"

        # Use job info from metadata if available, otherwise create basic info
        if export_context.metadata and export_context.metadata.job_info:
            job_info = export_context.metadata.job_info.copy()
        else:
            job_info = {
                "job_id": export_context.job_id,
                "export_format": export_context.format.value,
                "table_name": export_context.table_name,
                "database_name": export_context.database_name,
                "analysis_name": f"job_{export_context.job_id}",
                "request_id": str(export_context.request_id),
            }

        export_timestamp = datetime.now(timezone.utc).isoformat()

        conversion_metadata = {
            "filename": filename,
            "job_info": job_info,
            "request_id": str(export_context.request_id),
            "archive_parquet": True,  # Enable archiving for parquet files
            "export_timestamp": export_timestamp,
        }

        log.debug(
            f"Prepared conversion metadata for job {export_context.job_id}: "
            f"filename={filename} (request: {export_context.request_id})"
        )

        return conversion_metadata

    async def _log_connection_pool_status(
        self, export_context: ExportContext, operation_stage: str
    ) -> None:
        """
        Log detailed connection pool status information.

        This method provides comprehensive logging of connection pool health,
        utilization, and configuration for debugging and monitoring purposes.

        Args:
            export_context: Export context containing connection information
            operation_stage: Stage of the export operation (e.g., "before_streaming", "conversion_complete")
        """
        try:
            connection_manager = export_context.connection_manager
            if not connection_manager:
                log.warning(
                    f"No connection manager available for pool status logging "
                    f"(job: {export_context.job_id}, stage: {operation_stage})"
                )
                return

            # Get connection pool information
            pool_info = {
                "operation_stage": operation_stage,
                "job_id": export_context.job_id,
                "request_id": str(export_context.request_id),
                "table_name": export_context.table_name,
                "database_name": export_context.database_name,
            }

            # Try to get pool statistics if available
            if hasattr(connection_manager, "get_pool_stats"):
                try:
                    pool_stats = await connection_manager.get_pool_stats()
                    pool_info.update(
                        {
                            "pool_size": pool_stats.get("pool_size"),
                            "active_connections": pool_stats.get("active_connections"),
                            "idle_connections": pool_stats.get("idle_connections"),
                            "pending_requests": pool_stats.get("pending_requests"),
                        }
                    )
                except Exception as e:
                    log.debug(f"Could not retrieve pool stats: {e}")
                    pool_info["pool_stats_error"] = str(e)

            # Try to get connection health information
            if hasattr(connection_manager, "check_health"):
                try:
                    health_status = await connection_manager.check_health()
                    pool_info["health_status"] = health_status
                except Exception as e:
                    log.debug(f"Could not check connection health: {e}")
                    pool_info["health_check_error"] = str(e)

            # Get connection manager type and configuration
            pool_info.update(
                {
                    "connection_manager_type": type(connection_manager).__name__,
                    "connection_manager_id": getattr(
                        connection_manager, "id", "unknown"
                    ),
                }
            )

            # Log the comprehensive pool status
            log.info(
                f"Connection pool status [{operation_stage}] for job {export_context.job_id}: "
                f"manager={pool_info.get('connection_manager_type')}, "
                f"active={pool_info.get('active_connections', 'unknown')}, "
                f"idle={pool_info.get('idle_connections', 'unknown')}, "
                f"health={pool_info.get('health_status', 'unknown')} "
                f"(request: {export_context.request_id})"
            )

            # Log detailed information at debug level
            log.debug(f"Detailed connection pool info: {pool_info}")

        except Exception as e:
            log.error(
                f"Failed to log connection pool status for job {export_context.job_id} "
                f"at stage {operation_stage}: {e}"
            )

    async def _cleanup_parquet_stream(self, stream: Any) -> None:
        """
        Custom cleanup callback for parquet streams.

        This method handles proper cleanup of parquet data streams,
        ensuring that any underlying resources are properly released.

        Args:
            stream: The parquet stream to clean up
        """
        try:
            # Handle async generator streams
            if hasattr(stream, "aclose") and callable(getattr(stream, "aclose")):
                await stream.aclose()
                log.debug("Closed async parquet stream")
            # Handle regular generator streams
            elif hasattr(stream, "close") and callable(getattr(stream, "close")):
                stream.close()
                log.debug("Closed parquet stream")
            # Handle other stream types
            else:
                log.debug(
                    f"No specific cleanup method for stream type: {type(stream).__name__}"
                )

        except Exception as e:
            log.warning(f"Error during parquet stream cleanup: {e}")
            # Don't raise the exception as cleanup should be best-effort

    async def _record_export_access(
        self, job_id: int, format: ExportFormat, request_id: str
    ) -> None:
        """
        Record the export access in the result_access table.

        This matches the behavior of the original export system.

        Args:
            job_id: ID of the job being exported
            format: Export format being used
            request_id: Request ID for tracking
        """
        try:
            access_record_query = f"""
                INSERT INTO kpi_results.result_access
                (result_id, user_id, access_type, details)
                VALUES
                ('job_{job_id}', 'anonymous', 'export', 'Export format: {format.value}, Job ID: {job_id}, Request ID: {request_id}')
            """

            await ClickHouseHandler.execute_command(
                command=access_record_query, query_id=f"access_record_{request_id}"
            )

            log.info(
                f"Recorded export access for job_id {job_id} (request: {request_id})"
            )

        except Exception as e:
            # Log but don't fail if recording access fails (matching original behavior)
            log.warning(f"Failed to record export access for job {job_id}: {e}")
