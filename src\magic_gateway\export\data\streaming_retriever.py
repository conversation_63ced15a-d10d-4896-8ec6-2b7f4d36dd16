"""
Streaming data retrieval for export operations.

This module provides efficient data retrieval using SQLAlchemy streaming queries
from ClickHouse with support for both vertical and horizontal layouts.
"""

import asyncio
import uuid
from typing import AsyncGenerator, Dict, List, Optional, Any

from magic_gateway.core.logging_config import log
from magic_gateway.db.connection_manager import ClickHouseConnectionManager
from magic_gateway.export.exceptions import (
    StreamingDataError,
    TableNotFoundError,
    ConnectionTimeoutError,
    ExportErrorContext,
)
from magic_gateway.export.models import ExcelLayout, JobMetadata


class ChunkingStrategy:
    """Strategy for chunking large datasets during streaming."""
    
    def __init__(
        self,
        use_period_chunking: bool = False,
        chunk_size: int = 50000,
        periods: Optional[List[str]] = None
    ):
        self.use_period_chunking = use_period_chunking
        self.chunk_size = chunk_size
        self.periods = periods or []


class StreamingDataRetriever:
    """
    Handles efficient data retrieval using SQLAlchemy streaming queries.
    
    This class provides streaming data retrieval from ClickHouse with support
    for both vertical (database structure) and horizontal (facts as columns) layouts.
    It uses the existing ClickHouse connection manager for connection pooling.
    """
    
    def __init__(self, connection_manager: ClickHouseConnectionManager):
        """
        Initialize the streaming data retriever.
        
        Args:
            connection_manager: ClickHouse connection manager instance
        """
        self.connection_manager = connection_manager
        self.cluster_name = getattr(connection_manager, 'cluster_name', 'primary')
    
    async def stream_job_data_raw(
        self,
        job_id: int,
        table_name: str,
        chunking_strategy: ChunkingStrategy,
        request_id: Optional[str] = None,
        layout: ExcelLayout = ExcelLayout.VERTICAL,
        facts_list: Optional[List[str]] = None,
        axes_info: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[List[Dict[str, Any]], None]:
        """
        Stream job data using SQLAlchemy streaming queries.
        
        This method provides memory-efficient streaming of large datasets by
        processing data in chunks and yielding results incrementally.
        Works with the actual table structure that has axis columns, position columns,
        period_name, Fact, and Value.
        
        Args:
            job_id: Job ID to retrieve data for
            table_name: Fully qualified table name (database.table)
            chunking_strategy: Strategy for chunking the data
            request_id: Optional request ID for error tracking
            layout: Layout type (vertical or horizontal)
            facts_list: List of facts for horizontal layout
            axes_info: Axes information from metadata for column names
            
        Yields:
            Lists of dictionaries representing rows of data
            
        Raises:
            StreamingDataError: If streaming fails
            TableNotFoundError: If the table doesn't exist
            ConnectionTimeoutError: If connection times out
        """
        request_id = request_id or str(uuid.uuid4())
        
        with ExportErrorContext(request_id, "stream_job_data_raw", job_id) as ctx:
            ctx.add_context("table_name", table_name)
            ctx.add_context("layout", layout.value)
            ctx.add_context("chunk_size", chunking_strategy.chunk_size)
            
            if not self.connection_manager.initialized:
                raise StreamingDataError(
                    request_id=request_id,
                    operation="stream_job_data_raw",
                    error_details="Connection manager is not initialized",
                    context={
                        "job_id": job_id,
                        "table_name": table_name,
                        "cluster_name": self.cluster_name
                    }
                )
            
            try:
                # Validate axes_info for pivot queries
                if layout == ExcelLayout.HORIZONTAL and not axes_info:
                    raise StreamingDataError(
                        request_id=request_id,
                        operation="stream_job_data_raw",
                        error_details="axes_info is required for horizontal layout",
                        context={
                            "job_id": job_id,
                            "table_name": table_name,
                            "layout": layout.value
                        }
                    )
                
                # Generate the appropriate query based on layout
                if layout == ExcelLayout.HORIZONTAL and facts_list and axes_info:
                    # For horizontal layout with period chunking, we'll handle queries per period
                    if chunking_strategy.use_period_chunking and chunking_strategy.periods:
                        # Use base pivot query without period filter - periods will be added during streaming
                        query = await self.get_pivot_query(job_id, facts_list, table_name, axes_info)
                    else:
                        # Single pivot query for all data
                        query = await self.get_pivot_query(job_id, facts_list, table_name, axes_info)
                else:
                    query = await self.get_streaming_query(job_id, table_name, layout, axes_info)
                
                ctx.add_context("query_type", "pivot" if layout == ExcelLayout.HORIZONTAL else "standard")
                ctx.add_context("facts_count", len(facts_list) if facts_list else 0)
                ctx.add_context("axes_count", len(axes_info) if axes_info else 0)
                
                log.debug(f"Starting data streaming for job {job_id} using {layout.value} layout")
                
                # Stream data based on chunking strategy
                if chunking_strategy.use_period_chunking and chunking_strategy.periods:
                    log.debug(f"Using period-based chunking for {len(chunking_strategy.periods)} periods")

                    # For pivot queries, pass the parameters needed to regenerate queries with period filters
                    pivot_params = None
                    if layout == ExcelLayout.HORIZONTAL and facts_list and axes_info:
                        pivot_params = {
                            'job_id': job_id,
                            'facts_list': facts_list,
                            'table_name': table_name,
                            'axes_info': axes_info
                        }
                        log.debug(f"Created pivot_params for horizontal layout: job_id={job_id}, facts_count={len(facts_list)}, axes_count={len(axes_info)}")
                    else:
                        log.debug(f"No pivot_params created: layout={layout.value}, facts_list={bool(facts_list)}, axes_info={bool(axes_info)}")

                    async for chunk in self._stream_by_periods(
                        query, chunking_strategy, request_id, job_id, pivot_params
                    ):
                        yield chunk
                else:
                    log.debug(f"Using continuous streaming with chunk size {chunking_strategy.chunk_size}")
                    async for chunk in self._stream_continuous(
                        query, chunking_strategy.chunk_size, request_id, job_id
                    ):
                        yield chunk
                        
                log.debug(f"Completed data streaming for job {job_id}")
                
            except Exception as e:
                error_msg = str(e).lower()
                
                # Classify the error type
                if "table" in error_msg and ("not found" in error_msg or "doesn't exist" in error_msg):
                    database_name, table_only = table_name.split('.', 1) if '.' in table_name else ('unknown', table_name)
                    raise TableNotFoundError(
                        request_id=request_id,
                        job_id=job_id,
                        table_name=table_only,
                        database_name=database_name,
                        context={
                            "full_table_name": table_name,
                            "cluster_name": self.cluster_name
                        }
                    ) from e
                elif "timeout" in error_msg or "connection" in error_msg:
                    raise ConnectionTimeoutError(
                        request_id=request_id,
                        timeout_seconds=30.0,  # Default timeout
                        operation="stream_job_data_raw",
                        context={
                            "job_id": job_id,
                            "table_name": table_name,
                            "error": str(e)
                        }
                    ) from e
                else:
                    raise StreamingDataError(
                        request_id=request_id,
                        operation="stream_job_data_raw",
                        error_details=str(e),
                        context={
                            "job_id": job_id,
                            "table_name": table_name,
                            "layout": layout.value,
                            "cluster_name": self.cluster_name
                        }
                    ) from e
    
    async def get_streaming_query(
        self,
        job_id: int,
        table_name: str,
        layout: ExcelLayout = ExcelLayout.VERTICAL,
        axes_info: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Generate optimized SQL query for streaming data retrieval.
        
        This method creates SQL queries optimized for streaming large datasets
        with appropriate ordering and structure for the specified layout.
        The actual table structure has axis columns, position columns, period_name, Fact, and Value.
        
        Args:
            job_id: Job ID to filter data (not used as filter since table is job-specific)
            table_name: Fully qualified table name (already job-specific)
            layout: Layout type (vertical or horizontal)
            axes_info: Optional axes information from metadata for column ordering
            
        Returns:
            SQL query string optimized for streaming
        """
        # Generate ORDER BY clause based on axes_info if available
        order_by_columns = self._generate_order_by_columns(axes_info)
        
        # For vertical layout, select all columns with proper ordering
        query = f"""
        SELECT *
        FROM {table_name}
        ORDER BY {order_by_columns}
        """
        
        return query.strip()
    
    def _generate_order_by_columns(self, axes_info: Optional[Dict[str, Any]]) -> str:
        """
        Generate ORDER BY clause based on axes information.
        
        Args:
            axes_info: Axes information from metadata
            
        Returns:
            ORDER BY clause string
        """
        if not axes_info:
            # Fallback to generic ordering
            return "period_name, Fact"
        
        # Extract axis names and create position-based ordering
        order_columns = []
        
        # Add position columns for ordering (these provide the hierarchical structure)
        for axis_key, axis_data in axes_info.items():
            if isinstance(axis_data, dict) and 'name' in axis_data:
                axis_name = axis_data['name']
                position_column = f"{axis_name}_position_number"
                order_columns.append(position_column)
        
        # Add period and fact for final ordering
        order_columns.extend(["period_name", "Fact"])
        
        return ", ".join(order_columns) if order_columns else "period_name, Fact"
    
    async def get_pivot_query(
        self,
        job_id: int,
        facts_list: List[str],
        table_name: str,
        axes_info: Dict[str, Any],
        period_filter: Optional[str] = None
    ) -> str:
        """
        Generate SQL pivot query to transform facts into columns.
        
        This method creates a ClickHouse-specific pivot query that transforms
        the vertical fact structure into a horizontal layout where each fact
        becomes a column. Works with the actual table structure that has
        axis columns, position columns, period_name, Fact, and Value.
        
        Args:
            job_id: Job ID (not used as filter since table is job-specific)
            facts_list: List of fact names to pivot into columns
            table_name: Fully qualified table name (already job-specific)
            axes_info: Axes information from metadata for column names
            period_filter: Optional period to filter by for chunking
            
        Returns:
            SQL pivot query string
        """
        if not facts_list:
            # Fallback to standard query if no facts provided
            return await self.get_streaming_query(job_id, table_name, ExcelLayout.VERTICAL, axes_info)
        
        # Extract axis column names from axes_info
        axis_columns = []
        position_columns = []
        
        for axis_key, axis_name in axes_info.items():
            # Extract the base axis name before any metadata in parentheses
            if isinstance(axis_name, str) and " (" in axis_name:
                axis_name = axis_name.split(" (")[0].strip()
            elif isinstance(axis_name, str) and "." in axis_name:
                axis_name = axis_name.split(".")[1].strip()[5:]
            else:    
                axis_name = str(axis_name[5:])
            axis_columns.append(axis_key + "_" + axis_name)
            position_columns.append(f"{axis_name}_position_number")
        
        # Create pivot columns for each fact
        pivot_columns = []
        for fact in facts_list:
            # Use ClickHouse's conditional aggregation for pivot
            # The actual table has 'Fact' and 'Value' columns (note capitalization)
            pivot_columns.append(
                f"sumIf(Value, Fact = '{self._escape_sql_string(fact)}') AS `{self._escape_sql_string(fact)}`"
            )
        
        pivot_columns_str = ",\n    ".join(pivot_columns)
        
        # Build SELECT clause with axis columns
        select_columns = ["period_name"] + axis_columns + position_columns
        select_columns_str = ",\n    ".join(select_columns)
        
        # Build WHERE clause
        where_conditions = []
        if period_filter:
            escaped_period = self._escape_sql_string(period_filter)
            where_conditions.append(f"period_name = '{escaped_period}'")
            log.debug(f"Added period filter to pivot query: period_name = '{escaped_period}'")
        else:
            log.debug("No period filter applied to pivot query")

        where_clause = f"WHERE {' AND '.join(where_conditions)}" if where_conditions else ""
        
        # Build GROUP BY clause
        group_by_columns = ["period_name"] + axis_columns + position_columns
        group_by_str = ", ".join(group_by_columns)
        
        # Build ORDER BY clause using position columns for proper hierarchical ordering
        order_by_str = ", ".join(position_columns + ["period_name"])
        
        # Generate the pivot query with proper ClickHouse syntax
        query = f"""
        SELECT 
            {select_columns_str},
            {pivot_columns_str}
        FROM {table_name}
        {where_clause}
        GROUP BY {group_by_str}
        ORDER BY {order_by_str}
        """
        
        return query.strip()
    
    async def get_pivot_query_with_chunking(
        self,
        job_id: int,
        facts_list: List[str],
        table_name: str,
        axes_info: Dict[str, Any],
        periods: List[str]
    ) -> List[str]:
        """
        Generate multiple pivot queries for period-based chunking.
        
        This method creates separate pivot queries for each period to support
        efficient processing of large datasets by breaking them into manageable chunks.
        
        Args:
            job_id: Job ID to filter data
            facts_list: List of fact names to pivot into columns
            table_name: Fully qualified table name
            axes_info: Axes information from metadata for column names
            periods: List of periods to create separate queries for
            
        Returns:
            List of SQL pivot query strings, one per period
        """
        queries = []
        
        for period in periods:
            query = await self.get_pivot_query(
                job_id=job_id,
                facts_list=facts_list,
                table_name=table_name,
                axes_info=axes_info,
                period_filter=period
            )
            queries.append(query)
        
        return queries
    
    async def _stream_continuous(
        self,
        query: str,
        chunk_size: int,
        request_id: str,
        job_id: int
    ) -> AsyncGenerator[List[Dict[str, Any]], None]:
        """
        Stream data continuously using LIMIT/OFFSET pagination.
        
        Args:
            query: SQL query to execute
            chunk_size: Number of rows per chunk
            request_id: Request ID for error tracking
            job_id: Job ID for logging
            
        Yields:
            Chunks of data as lists of dictionaries
        """
        offset = 0
        rows_processed = 0
        
        try:
            async with self.connection_manager.connection() as conn:
                while True:
                    # Add LIMIT and OFFSET to the query
                    paginated_query = f"{query} LIMIT {chunk_size} OFFSET {offset}"
                    
                    # Execute query in thread pool since ClickHouse driver is synchronous
                    result = await asyncio.get_event_loop().run_in_executor(
                        None, conn.execute, paginated_query
                    )
                    
                    if not result:
                        # No more data
                        break
                    
                    # Convert result to list of dictionaries
                    # Get column names from the first query if available
                    if hasattr(conn, 'last_query') and hasattr(conn.last_query, 'columns_with_types'):
                        columns = [col[0] for col in conn.last_query.columns_with_types]
                    else:
                        # Fallback: assume standard column structure
                        columns = self._get_default_columns(len(result[0]) if result else 0)
                    
                    chunk_data = []
                    for row in result:
                        row_dict = dict(zip(columns, row))
                        chunk_data.append(row_dict)
                    
                    rows_processed += len(chunk_data)
                    
                    # Log progress for large datasets
                    if rows_processed % 1000000 == 0:  # Every 1M rows
                        log.debug(f"Processed {rows_processed:,} rows for job {job_id}")
                    
                    yield chunk_data
                    
                    # If we got fewer rows than chunk_size, we're done
                    if len(result) < chunk_size:
                        break
                    
                    offset += chunk_size
                    
        except Exception as e:
            raise StreamingDataError(
                request_id=request_id,
                operation="continuous_streaming",
                error_details=str(e),
                rows_processed=rows_processed,
                context={
                    "job_id": job_id,
                    "offset": offset,
                    "chunk_size": chunk_size
                }
            ) from e
        
        log.debug(f"Completed continuous streaming for job {job_id}, processed {rows_processed:,} rows")
    
    async def _stream_by_periods(
        self,
        base_query: str,
        chunking_strategy: ChunkingStrategy,
        request_id: str,
        job_id: int,
        pivot_params: Optional[Dict[str, Any]] = None
    ) -> AsyncGenerator[List[Dict[str, Any]], None]:
        """
        Stream data by periods for large datasets.

        This method processes data period by period to reduce memory usage
        and improve performance for very large datasets. Supports both standard
        and pivot queries with period-based chunking.

        Args:
            base_query: Base SQL query
            chunking_strategy: Chunking strategy with periods
            request_id: Request ID for error tracking
            job_id: Job ID for logging
            pivot_params: Optional parameters for regenerating pivot queries with period filters.
                         Contains job_id, facts_list, table_name, and axes_info for proper
                         pivot query generation with period_filter parameter.

        Yields:
            Chunks of data as lists of dictionaries
        """
        total_rows_processed = 0
        current_period = None
        
        try:
            for period in chunking_strategy.periods:
                current_period = period

                # Validate that period is not empty
                if not period or not str(period).strip():
                    raise StreamingDataError(
                        request_id=request_id,
                        operation="period_chunking",
                        error_details="Empty or invalid period value encountered during period chunking",
                        context={
                            "job_id": job_id,
                            "period_value": repr(period),
                            "all_periods": chunking_strategy.periods,
                            "period_index": chunking_strategy.periods.index(period) if period in chunking_strategy.periods else -1
                        }
                    )

                log.debug(f"Processing period '{period}' for job {job_id}")
                
                # For pivot queries, we need to handle period filtering differently
                if "GROUP BY" in base_query.upper() and "sumIf" in base_query:
                    # This is a pivot query - must use proper get_pivot_query method with period_filter
                    if not pivot_params:
                        raise StreamingDataError(
                            request_id=request_id,
                            operation="period_chunking_pivot",
                            error_details="pivot_params is required for pivot queries with period chunking",
                            context={
                                "job_id": job_id,
                                "current_period": period,
                                "query_type": "pivot",
                                "base_query_preview": base_query[:200] + "..." if len(base_query) > 200 else base_query
                            }
                        )

                    # Validate that all required parameters are present
                    required_params = ['job_id', 'facts_list', 'table_name', 'axes_info']
                    missing_params = [param for param in required_params if param not in pivot_params]
                    if missing_params:
                        raise StreamingDataError(
                            request_id=request_id,
                            operation="period_chunking_pivot",
                            error_details=f"Missing required pivot_params: {missing_params}",
                            context={
                                "job_id": job_id,
                                "current_period": period,
                                "available_params": list(pivot_params.keys()),
                                "missing_params": missing_params
                            }
                        )

                    # Use the get_pivot_query method with period_filter parameter
                    period_query = await self.get_pivot_query(
                        job_id=pivot_params['job_id'],
                        facts_list=pivot_params['facts_list'],
                        table_name=pivot_params['table_name'],
                        axes_info=pivot_params['axes_info'],
                        period_filter=period
                    )
                    log.debug(f"Generated pivot query with period filter for period '{period}' (period_filter='{period}')")
                else:
                    # Standard query - add period filter using the correct column name
                    period_query = self._add_period_filter_to_standard_query(base_query, period)
                
                # Stream this period's data
                period_rows = 0
                async for chunk in self._stream_continuous(
                    period_query, chunking_strategy.chunk_size, request_id, job_id
                ):
                    period_rows += len(chunk)
                    total_rows_processed += len(chunk)
                    yield chunk
                
                log.debug(f"Completed period '{period}' for job {job_id}: {period_rows:,} rows")
                
        except Exception as e:
            raise StreamingDataError(
                request_id=request_id,
                operation="period_streaming",
                error_details=str(e),
                rows_processed=total_rows_processed,
                context={
                    "job_id": job_id,
                    "periods": chunking_strategy.periods,
                    "current_period": current_period
                }
            ) from e
        
        log.debug(f"Completed period-based streaming for job {job_id}, processed {total_rows_processed:,} rows")
    
    def _get_default_columns(self, column_count: int) -> List[str]:
        """
        Get default column names when column metadata is not available.
        
        The actual table structure has axis columns, position columns, period_name, Fact, and Value.
        This provides a fallback when column metadata cannot be retrieved.
        
        Args:
            column_count: Number of columns in the result
            
        Returns:
            List of default column names
        """
        if column_count == 0:
            return []
        
        # Standard column structure for job results based on actual table structure
        # This is a fallback - actual column names should come from table metadata
        standard_columns = [
            'period_name', 'Fact', 'Value'  # Core columns that are always present
        ]
        
        # Add generic axis and position columns
        for i in range(1, 5):  # Assume up to 4 axes
            standard_columns.extend([
                f'axis_{i}',
                f'axis_{i}_position_number'
            ])
        
        if column_count <= len(standard_columns):
            return standard_columns[:column_count]
        else:
            # Add generic column names for additional columns
            extra_columns = [f'column_{i}' for i in range(len(standard_columns), column_count)]
            return standard_columns + extra_columns
    
    def _escape_sql_string(self, value: str) -> str:
        """
        Escape SQL string values to prevent injection and handle special characters.
        
        Args:
            value: String value to escape
            
        Returns:
            Escaped string value
        """
        if not isinstance(value, str):
            return str(value)
        
        # Escape single quotes by doubling them (ClickHouse standard)
        return value.replace("'", "''")
    
    def _escape_column_name(self, column_name: str) -> str:
        """
        Escape column names for use in SQL queries.
        
        Args:
            column_name: Column name to escape
            
        Returns:
            Escaped column name
        """
        if not isinstance(column_name, str):
            return str(column_name)
        
        # Remove or replace characters that could cause issues in column names
        # Keep alphanumeric, underscore, and basic punctuation
        escaped = ''.join(c if c.isalnum() or c in '_-.' else '_' for c in column_name)
        
        # Ensure it doesn't start with a number
        if escaped and escaped[0].isdigit():
            escaped = f"col_{escaped}"
        
        return escaped or "unnamed_column"
    


    def _add_period_filter_to_standard_query(self, base_query: str, period: str) -> str:
        """
        Add period filter to a standard query, handling ORDER BY clauses correctly.

        Args:
            base_query: Original SQL query
            period: Period value to filter by

        Returns:
            Modified query with period filter inserted before ORDER BY
        """
        escaped_period = self._escape_sql_string(period)

        if "WHERE" in base_query.upper():
            # Add to existing WHERE clause
            return f"{base_query} AND period_name = '{escaped_period}'"
        else:
            # Need to insert WHERE clause before ORDER BY if it exists
            query_upper = base_query.upper()
            order_by_index = query_upper.find("ORDER BY")

            if order_by_index != -1:
                # Insert WHERE clause before ORDER BY
                before_order = base_query[:order_by_index].strip()
                order_clause = base_query[order_by_index:]
                return f"{before_order} WHERE period_name = '{escaped_period}' {order_clause}"
            else:
                # No ORDER BY, add WHERE at the end
                return f"{base_query} WHERE period_name = '{escaped_period}'"